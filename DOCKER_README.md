# Docker Setup for Face Recognition App

This setup provides a containerized environment for the Face Recognition React application using Docker and nginx.

## Files Created

- `docker-compose.yaml` - Main orchestration file
- `Dockerfile` - Multi-stage build configuration
- `nginx.conf` - Nginx server configuration
- `.dockerignore` - Files to exclude from Docker build context

## Quick Start

### Prerequisites
- Docker and Docker Compose installed on your system
- Make sure ports 3000 is available

### Build and Run

1. **Build and start the application:**
   ```bash
   docker-compose up --build
   ```

2. **Run in detached mode (background):**
   ```bash
   docker-compose up -d --build
   ```

3. **Access the application:**
   - Open your browser and navigate to: `http://localhost:3000`

### Management Commands

- **Stop the application:**
  ```bash
  docker-compose down
  ```

- **View logs:**
  ```bash
  docker-compose logs -f
  ```

- **Rebuild after code changes:**
  ```bash
  docker-compose down
  docker-compose up --build
  ```

- **Remove containers and images:**
  ```bash
  docker-compose down --rmi all
  ```

## Configuration Details

### Docker Compose
- **Service Name:** `face-recognition-app`
- **Container Name:** `face-recognition-frontend`
- **Port Mapping:** `3000:80` (host:container)
- **Network:** `face-recognition-network`

### Dockerfile
- **Multi-stage build** for optimized production image
- **Stage 1:** Node.js 18 Alpine for building the React app
- **Stage 2:** Nginx Alpine for serving static files
- **Build command:** `npm run build` (uses Vite)

### Nginx Configuration
- **Port:** 80 (mapped to host port 3000)
- **Document Root:** `/usr/share/nginx/html`
- **Features:**
  - Gzip compression enabled
  - Security headers configured
  - SPA routing support (React Router)
  - Static asset caching (1 year)
  - API proxy to backend (************:8000)

### Environment Variables
The application uses these environment variables from `.env`:
- `VITE_BASE_URL=http://************:8000`
- `VITE_IMAGE_URL=http://************:8001`

## Customization

### Changing Ports
To use a different port, modify the `docker-compose.yaml`:
```yaml
ports:
  - "8080:80"  # Change 3000 to your desired port
```

### Backend API Configuration
The nginx configuration includes a proxy for API requests. If your backend URL changes, update the `nginx.conf`:
```nginx
location /api/ {
    proxy_pass http://YOUR_BACKEND_URL/;
    # ... other proxy settings
}
```

### Environment Variables
To modify environment variables, update your `.env` file before building:
```bash
VITE_BASE_URL=http://your-backend-url:port
VITE_IMAGE_URL=http://your-image-server:port
```

## Troubleshooting

### Common Issues

1. **Port already in use:**
   ```bash
   # Check what's using port 3000
   lsof -i :3000
   # Kill the process or change the port in docker-compose.yaml
   ```

2. **Build fails:**
   ```bash
   # Clean Docker cache
   docker system prune -a
   # Rebuild
   docker-compose up --build --force-recreate
   ```

3. **Application not loading:**
   - Check if the container is running: `docker-compose ps`
   - Check logs: `docker-compose logs`
   - Verify nginx configuration: `docker-compose exec face-recognition-app nginx -t`

4. **API requests failing:**
   - Verify backend is accessible from the container
   - Check nginx proxy configuration in `nginx.conf`
   - Ensure environment variables are correct

### Development vs Production

This setup is optimized for production. For development, you might want to:
- Use volume mounts for live reloading
- Expose additional ports for development tools
- Use a different nginx configuration

## Security Considerations

- The nginx configuration includes basic security headers
- Consider adding SSL/TLS for production deployments
- Review and adjust Content Security Policy as needed
- Ensure backend APIs have proper authentication and authorization

## Performance

- Gzip compression is enabled for better performance
- Static assets are cached for 1 year
- Multi-stage build reduces final image size
- Alpine Linux base images for smaller footprint

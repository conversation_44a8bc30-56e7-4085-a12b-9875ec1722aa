version: '3.8'

services:
  face-recognition-app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:80"
    environment:
      - NODE_ENV=production
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    restart: unless-stopped
    container_name: face-recognition-frontend
    networks:
      - face-recognition-network

networks:
  face-recognition-network:
    driver: bridge

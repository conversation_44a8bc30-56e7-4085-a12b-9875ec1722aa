{"name": "face-recognition-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@mediapipe/camera_utils": "^0.3.1675466862", "@mediapipe/face_mesh": "^0.4.1633559619", "@radix-ui/react-alert-dialog": "^1.1.10", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-query": "^5.66.9", "@tanstack/react-router": "^1.98.3", "@tanstack/react-table": "^8.21.2", "axios": "^1.8.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "html2canvas": "^1.4.1", "i18next": "^24.2.3", "lucide-react": "^0.474.0", "next-themes": "^0.4.6", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-i18next": "^15.4.1", "recharts": "^2.15.1", "sonner": "^2.0.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.17.0", "@tanstack/eslint-plugin-query": "^5.66.1", "@tanstack/router-devtools": "^1.98.3", "@tanstack/router-plugin": "^1.98.3", "@types/node": "^22.12.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "vite": "^6.0.5"}}
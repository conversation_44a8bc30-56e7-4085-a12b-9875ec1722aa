import { apiInstance } from "@/lib/axios/axiosInstance";

export const authSignIn = async (credentials) => {
  try {
    const response = await apiInstance.post("/auth/login", credentials);
    return response;
  } catch (error) {
    throw error;
  }
};

export const fetchCurrentUser = async (accessToken) => {
  const response = await apiInstance.get("/srv/security/security/me/", {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  return response.data;
};

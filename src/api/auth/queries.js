import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { authSignIn, fetchCurrentUser } from "./fetchers";
import { toast } from "sonner";
import { authStore, getToken } from "@/store/auth/auth-store";
import { useNavigate } from "@tanstack/react-router";

const QueryKeys = {
  USER: "user",
};

export function useLogin() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (credentials) => {
      const response = await authSignIn(credentials);
      const { access_token } = response.data;

      const user = await fetchCurrentUser(access_token);
      return { access_token, user };
    },
    onSuccess: ({ access_token, user }) => {
      authStore.getState().signIn({ access_token, user });

      queryClient.setQueryData([QueryKeys.USER], user);
      navigate({ to: "/dashboard" });
    },
    onError: (error) => {
      toast.error(error.response?.data?.detail || "Login failed");
    },
  });
}

export const useCurrentUser = () => {
  const token = getToken();
  return useQuery({
    queryKey: [QueryKeys.USER],
    queryFn: () => fetchCurrentUser(token),
    enabled: Boolean(token),
    onError: (error) => {
      console.error("Failed to fetch user", error);
      if (err.response?.status === 401) {
        authStore.getState().logout();
      }
    },
  });
};

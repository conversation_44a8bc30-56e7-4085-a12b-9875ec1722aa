import { apiInstance } from "@/lib/axios/axiosInstance.js";

export async function changeUsername({ new_username }) {
    const response = await apiInstance.post(
        `user/change-username`,
        { new_username }
    );
    return response.data;
}

export async function changePassword({ old_password, new_password }) {
    const response = await apiInstance.post(
        `user/change-password`,
        { old_password, new_password }
    );
    return response.data;
}

import {
    useMutation,
    useQueryClient,
} from "@tanstack/react-query";
import {changePassword, changeUsername} from "./fetchers";

const QueryKeys = {
    USERNAME: "USERNAME",
    PASSWORD: "PASSWORD",
};

export const useChangeUsername = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (new_username) => changeUsername(new_username),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: QueryKeys.USERNAME });
            console.log("Username Updated!");
        },
        onError: (error) => {
            console.log(error.response?.data);
        },
    });
};

export const useChangePassword = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (old_password, new_password) => changePassword(old_password, new_password),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: QueryKeys.PASSWORD });
            console.log("Password Updated!");
        },
        onError: (error) => {
            console.log(error.response?.data);
        },
    });
};
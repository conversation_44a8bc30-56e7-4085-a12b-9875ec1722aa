import { apiInstance } from "@/lib/axios/axiosInstance";

export async function getStaff({
  skip,
  pageSize,
  search,
  on_campus,
  department,
}) {
  const response = await apiInstance.get("/srv/security/security/staff", {
    params: { skip, limit: pageSize, search, on_campus, department },
  });
  return response.data;
}

export async function getStaffSecurity() {
  const response = await apiInstance.get("/srv/security/security/staff", {
    params: { department: "Security" },
  });
  return response.data;
}

export async function getStaffById(id) {
  const response = await apiInstance.get(`srv/security/security/staff/${id}`);
  return response.data;
}

export async function createStaff(newStaff) {
  const response = await apiInstance.post(
    `srv/security/security/staff`,
    newStaff
  );
  return response.data;
}

export const updateStaff = async (id, updatedData) => {
  const response = await apiInstance.put(
    `srv/security/demographic/staff/${id}`,
    updatedData
  );
  return response.data;
};

export async function deleteStaff(staffId) {
  const response = await apiInstance.delete(
    `/srv/security/demographic/staff/${staffId}`
  );
  return response.data;
}

export async function getStaffImagesById(staffId) {
  const response = await apiInstance.get(
    `/srv/security/security/face_recognition/staff/image?person_id=${staffId}`
  );
  return response.data;
}

export async function predictStaffFace(formData) {
  const response = await apiInstance.post(
    `/srv/security/security/face_recognition/staff/predict`,
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }
  );
  return response.data;
}

export async function recordAction(record) {
  const response = await apiInstance.post(
    "/srv/security/security/record",
    record
  );
  return response.data;
}

export async function getStaffRecords({ skip, pageSize }) {
  const response = await apiInstance.get(
    "/srv/security/security/records/?entity=staff",
    {
      params: { skip, limit: pageSize },
    }
  );
  return response.data;
}

export async function getStaffRecordsByPersonId(personId) {
  const response = await apiInstance.get(
    `/srv/security/security/records/?person_id=${personId}`
  );
  return response.data;
}

export async function getStaffExcel() {
  const response = await apiInstance.get(
    "/srv/security/demographic/staff/export/",
    {
      responseType: "blob",
    }
  );
  return response.data;
}

import {
  keepPreviousData,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import {
  createStaff,
  deleteStaff,
  getStaff,
  getStaffById,
  getStaffExcel,
  getStaffImagesById,
  getStaffRecords,
  getStaffRecordsByPersonId,
  getStaffSecurity,
  predictStaffFace,
  recordAction,
  updateStaff,
} from "./fetchers";
import { toast } from "sonner";

const QueryKeys = {
  STAFF: "STAFF",
  STAFF_IMAGES: "STAFF_IMAGES",
  STAFF_RECORDS: "STAFF_RECORDS",
};

export const useStaff = ({ skip, pageSize, search, on_campus, department }) =>
  useQuery({
    queryKey: [
      QueryKeys.STAFF,
      { skip, pageSize, search, on_campus, department },
    ],
    queryFn: () => getStaff({ skip, pageSize, search, on_campus, department }),
    placeholderData: keepPreviousData,
  });

export const useStaffSecurity = () =>
  useQuery({
    queryKey: [QueryKeys.STAFF, { department: "Security" }],
    queryFn: () => getStaffSecurity(),
  });

export const useStaffById = (id) =>
  useQuery({
    queryKey: [QueryKeys.STAFF, id],
    queryFn: () => getStaffById(id),
    enabled: !!id,
  });

export const useCreateStaff = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (newStaff) => createStaff(newStaff),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QueryKeys.STAFF] });
    },
  });
};

export const useUpdateStaff = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, updatedData }) => updateStaff(id, updatedData),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: [QueryKeys.STAFF] });
      queryClient.invalidateQueries({ queryKey: [QueryKeys.STAFF, id] });
    },
    onError: (err) => {
      console.log(err);
    },
  });
};

export const useDeleteStaff = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id) => deleteStaff(id),
    onSuccess: () => {
      toast.success("Deleted");
    },
    onError: () => {
      toast.error("Failed to delete");
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QueryKeys.STAFF] });
    },
  });
};

export const useStaffImagesById = (staffId) =>
  useQuery({
    queryKey: [QueryKeys.STAFF_IMAGES, staffId],
    queryFn: () => getStaffImagesById(staffId),
  });

export const usePredictStaff = () =>
  useMutation({
    mutationFn: (formData) => predictStaffFace(formData),
    onSuccess: (data) => {
      console.log("Face recognized:", data);
      toast.success("Success");
    },
    onError: (err) => {
      console.error("Recognition error", err);
      toast.error("Error");
    },
  });

export const useRecordAction = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (record) => recordAction(record),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QueryKeys.RECORDS] });
      toast.success("Action recorded successfully");
    },
    onError: (err) => {
      console.error("Error recording action", err);
      toast.error("Failed to record action");
    },
  });
};

export const useStaffRecords = ({ skip, pageSize }) =>
  useQuery({
    queryKey: [QueryKeys.STAFF_RECORDS, { skip, pageSize }],
    queryFn: () => getStaffRecords({ skip, pageSize }),
    placeholderData: keepPreviousData,
  });

export const useStaffSelfRecords = (personId) =>
  useQuery({
    queryKey: [QueryKeys.STAFF_RECORDS, personId],
    queryFn: () => getStaffRecordsByPersonId(personId),
  });

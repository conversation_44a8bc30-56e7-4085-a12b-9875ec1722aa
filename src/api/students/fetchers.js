import { apiInstance } from "@/lib/axios/axiosInstance";

export async function getStudents({
  skip,
  pageSize,
  search,
  on_campus,
  department,
}) {
  const response = await apiInstance.get("/srv/security/security/students", {
    params: { skip, limit: pageSize, search, on_campus, department },
  });
  return response.data;
}

export async function getStudentById(id) {
  console.log(`srv/security/security/students/${id}`);
  const response = await apiInstance.get(
    `srv/security/security/students/${id}`
  );
  return response.data;
}

export async function createStudent(newStudent) {
  const response = await apiInstance.post(
    `srv/security/security/students`,
    newStudent
  );
  return response.data;
}

export async function deleteStudent(studentId) {
  const response = await apiInstance.delete(
    `/srv/security/demographic/student/${studentId}`
  );
  return response.data;
}

export async function getStudentImagesById(id) {
  const response = await apiInstance.get(
    `/srv/security/security/face_recognition/student/image?person_id=${id}`
  );
  return response.data;
}

export async function getStudentRecords({ skip, pageSize }) {
  const response = await apiInstance.get(
    "/srv/security/security/records/?entity=student",
    {
      params: { skip, limit: pageSize },
    }
  );
  return response.data;
}

export async function getStudentRecordsByPersonId(personId) {
  const response = await apiInstance.get(
    `/srv/security/security/records/?person_id=${personId}`
  );
  return response.data;
}

export async function getStudentProfileImageUrl(personId) {
  const response = await apiInstance.get(
    `/srv/security/demographic/persons/${personId}/profile_picture/`,
    {
      responseType: "blob",
    }
  );

  const blob = response.data;
  return URL.createObjectURL(blob);
}

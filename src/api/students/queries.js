import {
  keepPreviousData,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import {
  createStudent,
  deleteStudent,
  getStudentById,
  getStudentImagesById,
  getStudentProfileImageUrl,
  getStudentRecords,
  getStudentRecordsByPersonId,
  getStudents,
} from "./fetchers";

const QueryKeys = {
  STUDENTS: "STUDENTS",
  STUDENT_IMAGES: "STUDENT_IMAGES",
  STUDENT_RECORDS: "STUDENT_RECORDS",
  STUDENT_PROFILE_IMAGE: "STUDENT_PROFILE_IMAGE",
};

export const useStudents = ({
  skip,
  pageSize,
  search,
  on_campus,
  department,
}) =>
  useQuery({
    queryKey: [
      QueryKeys.STUDENTS,
      { skip, pageSize, search, on_campus, department },
    ],
    queryFn: () =>
      getStudents({ skip, pageSize, search, on_campus, department }),
    placeholderData: keepPreviousData,
  });

export const useStudentById = (id) =>
  useQuery({
    queryKey: [QueryKeys.STUDENTS, id],
    queryFn: () => getStudentById(id),
  });

export const useCreateStudent = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (newStudent) => createStudent(newStudent),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QueryKeys.STUDENTS] });
    },
  });
};

export const useDeleteStudent = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id) => deleteStudent(id),
    onSuccess: () => {
      toast.success("Deleted");
    },
    onError: () => {
      toast.error("Failed to delete");
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: [QueryKeys.STUDENTS] });
    },
  });
};

export const useStudentImagesById = (id) =>
  useQuery({
    queryKey: [QueryKeys.STUDENT_IMAGES, id],
    queryFn: () => getStudentImagesById(id),
  });

export const useStudentRecords = ({ skip, pageSize }) =>
  useQuery({
    queryKey: [QueryKeys.STUDENT_RECORDS, { skip, pageSize }],
    queryFn: () => getStudentRecords({ skip, pageSize }),
    placeholderData: keepPreviousData,
  });

export const useStudentSelfRecords = (personId) =>
  useQuery({
    queryKey: [QueryKeys.STUDENT_RECORDS, personId],
    queryFn: () => getStudentRecordsByPersonId(personId),
  });

export const useStudentProfileImage = (personId) =>
  useQuery({
    queryKey: [QueryKeys.STUDENT_PROFILE_IMAGE, personId],
    queryFn: () => getStudentProfileImageUrl(personId),
    enabled: !!personId,
  });

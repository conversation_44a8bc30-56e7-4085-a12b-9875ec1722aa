import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Trash2 } from "lucide-react";
import { useTranslation } from "react-i18next";

const ImagesList = ({ images: defaulImages }) => {
  const { t } = useTranslation();
  const [images, setImages] = useState(defaulImages || []);
  const [loading, setLoading] = useState(false);
  const [openModal, setOpenModal] = useState(false);

  // Simulate fetching images when person_Id changes
  // useEffect(() => {
  //   if (person_Id) {
  //     setLoading(true);
  //     // Simulate network delay
  //     setTimeout(() => {
  //       setImages(initialMockImages);
  //       setLoading(false);
  //     }, 500);
  //   }
  // }, [person_Id]);

  const handleDelete = (id) => {
    // Simulate delete
    setImages((prev) => prev.filter((img) => img.id !== id));
  };

  return (
    <div>
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">{t("matchingImages")}</h2>
        <Button onClick={() => setOpenModal(true)}>{t("uploadNew")}</Button>
      </div>

      {/* Images Grid */}
      <div className="grid grid-cols-3 gap-4 mt-4">
        {loading ? (
          <>{t("loading")}...</>
        ) : (
          images.map((item) => (
            <div key={item.id} className="relative">
              <img
                src={item.image || ""}
                alt=""
                className="w-full h-48 object-cover rounded"
              />
              <button
                onClick={() => handleDelete(item.id)}
                className="absolute top-1 left-1 bg-red-500 rounded-full p-1 text-white hover:bg-red-600"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          ))
        )}
      </div>

      {/* Upload Modal */}
      <Dialog open={openModal} onOpenChange={setOpenModal}>
        <DialogContent className="max-w-sm p-4">
          <ImageUploadForm
            person={1}
            onUpload={(newImage) => setImages((prev) => [...prev, newImage])}
            handleClose={() => setOpenModal(false)}
            loading={loading}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

const ImageUploadForm = ({ person, handleClose, onUpload, loading }) => {
  const [file, setFile] = useState(null);
  const { t } = useTranslation();

  const onFileChange = (e) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
    }
  };

  const fileUrl = file ? URL.createObjectURL(file) : "";
  const handleSubmit = (e) => {
    e.preventDefault();
    if (person && file) {
      // Mock upload delay
      setTimeout(() => {
        // Create a new image object
        const newImage = {
          id: Date.now(),
          image: fileUrl,
        };
        onUpload(newImage);
        handleClose();
      }, 500);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Image Preview */}
      <div className="w-full h-64 border-2 border-dashed rounded flex items-center justify-center">
        {fileUrl ? (
          <img
            src={fileUrl}
            alt="Preview"
            className="object-contain max-h-full"
          />
        ) : (
          <span className="text-muted">{t("noImageSelected")}</span>
        )}
      </div>

      {/* File Input */}
      <Input type="file" onChange={onFileChange} />

      {/* Submit Button */}
      <Button type="submit" disabled={loading} className="w-full">
        {loading ? "Loading..." : "Upload"}
      </Button>
    </form>
  );
};

export default ImagesList;

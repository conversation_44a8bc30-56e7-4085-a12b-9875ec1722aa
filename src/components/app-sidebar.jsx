import {
  Cctv,
  ChartSpline,
  Images,
  ListOrdered,
  LogOut,
  TvMinimal,
  UserPen,
  Users,
} from "lucide-react";

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { Link } from "@tanstack/react-router";
import { useTranslation } from "react-i18next";
import LogoutButton from "./auth/logout/logout-button";

// Sidebar Menu Data
const sidebarMenu = [
  {
    title: "main.title",
    items: [
      { name: "main.view", icon: TvMinimal, path: "/dashboard" },
      { name: "main.camera", icon: Cctv, path: "/dashboard/camera" },
    ],
  },
  {
    title: "lists.title",
    items: [
      { name: "lists.students", icon: Users, path: "/dashboard/students" },
      { name: "lists.staff", icon: Users, path: "/dashboard/staffs" },
    ],
  },
  {
    title: "records.title",
    items: [
      {
        name: "records.studentRecords",
        icon: ListOrdered,
        path: "/dashboard/student-records",
      },
      {
        name: "records.staffRecords",
        icon: ListOrdered,
        path: "/dashboard/staff-records",
      },
    ],
  },
  {
    title: "data.title",
    items: [
      {
        name: "data.statistics",
        icon: ChartSpline,
        path: "/dashboard/statistics",
      },
      { name: "data.images", icon: Images, path: "/dashboard/training-images" },
    ],
  },
  {
    title: "user.title",
    items: [
      { name: "user.profile", icon: UserPen, path: "/dashboard/profile" },
    ],
  },
];

const AppSidebar = () => {
  const { t } = useTranslation();

  return (
    <Sidebar>
      <SidebarHeader className="bg-slate-800 text-white">
        <div className="w-full font-bold text-2xl text-center">
          Face<span className="text-green-400">App</span>
        </div>
      </SidebarHeader>
      <SidebarContent className="bg-slate-800 text-slate-300">
        {sidebarMenu.map((menu, index) => (
          <SidebarGroup key={index}>
            <SidebarGroupLabel className="text-slate-300 uppercase select-none">
              {t(`appSidebar.${menu.title}`)}
            </SidebarGroupLabel>
            <SidebarGroupContent className={"select-none"}>
              <SidebarMenu>
                {menu.items.map((item) => (
                  <SidebarMenuItem key={item.name}>
                    <SidebarMenuButton asChild>
                      <Link
                        to={item.path}
                        className="flex items-center gap-2 p-4"
                      >
                        <item.icon className="h-7 w-7 text-green-500" />
                        <span className="text-base">
                          {" "}
                          {t(`appSidebar.${item.name}`)}
                        </span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}
      </SidebarContent>
      <SidebarFooter className="bg-slate-800 text-slate-300">
        <div className="w-full flex justify-center px-2">
          <LogoutButton className="w-full flex items-center gap-2 p-4 bg-slate-800 hover:bg-white hover:text-slate-800">
            <LogOut className="h-7 w-7 text-green-500" />
            <span className="text-base"> {t("appSidebar.user.logout")}</span>
          </LogoutButton>
        </div>
      </SidebarFooter>
    </Sidebar>
  );
};

export default AppSidebar;

import { BaseForm } from "@/components/forms/base-form";
import { Button } from "@/components/ui/button";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import useLoginForm from "./use-login-form";
import { Input } from "@/components/ui/input";

const LoginForm = () => {
  const { form, handleFormSubmit, isLoading } = useLoginForm();
  const { control } = form;
  return (
    <BaseForm
      form={form}
      onSubmit={handleFormSubmit}
      className={"space-y-4 w-full max-w-[300px] text-slate-700"}
    >
      <FormField
        control={control}
        name="username"
        render={({ field }) => (
          <FormItem className="space-y-1">
            <FormLabel>Username</FormLabel>
            <FormControl>
              <Input
                placeholder="Username"
                {...field}
                className="mt-0 m-0 h-11 bg-slate-100 border-transparent focus-visible:ring-blue-300"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name="password"
        render={({ field }) => (
          <FormItem className="space-y-1">
            <FormLabel>Password</FormLabel>
            <FormControl>
              <Input
                type="password"
                placeholder="Password"
                {...field}
                className="h-11 bg-slate-100 border-transparent focus-visible:ring-blue-300"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <Button
        type="submit"
        disabled={isLoading}
        className={"h-12 w-full bg-slate-700 hover:bg-slate-600"}
      >
        {isLoading ? "Signing in…" : "Sign In"}
      </Button>
    </BaseForm>
  );
};

export default LoginForm;

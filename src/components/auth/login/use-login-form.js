import { useForm } from "react-hook-form";
import { loginFormSchema } from "./login-schemas";
import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useLogin } from "@/api/auth/queries";

function useLoginForm() {
  const form = useForm({
    resolver: zod<PERSON><PERSON><PERSON>ver(loginFormSchema),
    defaultValues: {
      username: "",
      password: "",
    },
  });

  const loginMutation = useLogin();

  const handleFormSubmit = async (values) => {
    try {
      loginMutation.mutate(values);
    } catch (error) {
      console.error("Login failed:", error.response?.data?.detail);
    }
  };

  return { form, handleFormSubmit, isLoading: loginMutation.isPending };
}

export default useLoginForm;

import { authStore } from "@/store/auth/auth-store";
import { useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";

export const useLogoutButton = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const handleLogout = () => {
    queryClient.invalidateQueries(["user"]);
    authStore.getState().logout();
    navigate({ to: "/login" });
  };

  return {
    handleLogout,
  };
};

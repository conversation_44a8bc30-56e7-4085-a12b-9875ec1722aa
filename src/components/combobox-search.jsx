import React, { useState } from "react";
import {
  Popover,
  <PERSON><PERSON><PERSON>ontent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Check, ChevronsUpDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useDebounce } from "@/hooks/use-debounce";

export function ComboboxSearch({
  value,
  onChange,
  query,
  onQueryChange,
  options,
  loading,
  error,
  placeholder = "Search...",
  emptyMessage = "No options found",
  label = "Select...",
}) {
  const [open, setOpen] = useState(false);
  const selectedLabel =
    options.find((opt) => opt.value === value)?.label || label;

  return (
    <div className="relative">
      <Popover open={open} onOpenChange={setOpen} modal>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
            onClick={() => setOpen((prev) => !prev)}
          >
            {selectedLabel}
            <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
          </Button>
        </PopoverTrigger>

        <PopoverContent className="w-full p-0 z-50" side="bottom" align="start">
          <Command>
            <CommandInput
              placeholder={placeholder}
              className="h-8"
              value={query}
              onValueChange={onQueryChange}
            />
            <CommandList>
              {loading && <div className="p-2">Loading...</div>}
              {error && <div className="p-2 text-red-600">Failed to load</div>}
              {!loading && !error && (
                <>
                  <CommandEmpty>{emptyMessage}</CommandEmpty>
                  <CommandGroup>
                    {options.map((opt) => (
                      <CommandItem
                        key={opt.value}
                        value={String(opt.label)}
                        onSelect={() => {
                          onChange(opt.value);
                          setOpen(false);
                        }}
                      >
                        {opt.label}
                        {value === opt.value && (
                          <Check className="ml-auto h-4 w-4" />
                        )}
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}

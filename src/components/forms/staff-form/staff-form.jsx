import { Button } from "@/components/ui/button";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Calendar } from "@/components/ui/calendar";
import { Calendar as CalendarIcon, Send } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { format } from "date-fns";

import { BaseForm } from "../base-form";
import { cn } from "@/lib/utils";
import { useTranslation } from "react-i18next";
import { useStaffForm } from "./use-staff-form";

export default function EmployeeForm({
  isCreateForm = true,
  isLoading = false,
  initialData,
  onSubmit,
}) {
  const { form, handleSubmit } = useStaffForm({ initialData, onSubmit });
  const { control } = form;
  const {
    t,
    // i18n: { language },
  } = useTranslation();
  // const localeLng = language === "en" ? "en-US" : "ru-RU";

  return (
    <BaseForm
      form={form}
      onSubmit={handleSubmit}
      className="space-y-6 max-w-3xl mx-auto py-4"
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Job Title */}
        <FormField
          control={control}
          name="job_title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {t("staff.fields.jobTitle")}
                <span className="text-main-red">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder={t("staff.fields.placeholders.jobTitle")}
                  className="h-11 bg-slate-200 border-transparent focus-visible:ring-blue-300"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Address */}
        <FormField
          control={control}
          name="address"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {t("staff.fields.address")}
                <span className="text-main-red">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder={t("staff.fields.placeholders.address")}
                  className="h-11 bg-slate-200 border-transparent focus-visible:ring-blue-300"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Hire Date */}
        <FormField
          control={control}
          name="hire_date"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {t("staff.fields.hireDate")}
                <span className="text-main-red">*</span>
              </FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full h-11 bg-slate-200 border-transparent focus-visible:ring-blue-300",
                        !field.value && "text-muted-foreground"
                      )}
                    >
                      {field.value
                        ? format(field.value, "dd.MM.yyyy")
                        : t("staff.fields.placeholders.selectDate")}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent align="start" className="p-0">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Department */}
        <FormField
          control={control}
          name="department"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {t("staff.fields.department")}
                <span className="text-main-red">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder={t("staff.fields.placeholders.department")}
                  className="h-11 bg-slate-200 border-transparent focus-visible:ring-blue-300"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Person: Name */}
        <FormField
          control={control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {t("staff.fields.firstName")}
                <span className="text-main-red">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder={t("staff.fields.placeholders.firstName")}
                  className="h-11 bg-slate-200 border-transparent focus-visible:ring-blue-300"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Person: Surname */}
        <FormField
          control={control}
          name="surname"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {t("staff.fields.lastName")}
                <span className="text-main-red">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder={t("staff.fields.placeholders.lastName")}
                  className="h-11 bg-slate-200 border-transparent focus-visible:ring-blue-300"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Person: Middle Name */}
        <FormField
          control={control}
          name="middle_name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("staff.fields.middleName")}</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder={t("staff.fields.placeholders.middleName")}
                  className="h-11 bg-slate-200 border-transparent focus-visible:ring-blue-300"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Person: Email */}
        <FormField
          control={control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {t("staff.fields.email")}
                <span className="text-main-red">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  type="email"
                  placeholder="<EMAIL>"
                  className="h-11 bg-slate-200 border-transparent focus-visible:ring-blue-300"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Person: Phone Number */}
        <FormField
          control={control}
          name="phone_number"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("staff.fields.phoneNumber")}</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  type="tel"
                  placeholder="+1234567890"
                  className="h-11 bg-slate-200 border-transparent focus-visible:ring-blue-300"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Person: Date of Birth */}
        <FormField
          control={control}
          name="date_of_birth"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {t("staff.fields.dateOfBirth")}
                <span className="text-main-red">*</span>
              </FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full h-11 bg-slate-200 border-transparent focus-visible:ring-blue-300",
                        !field.value && "text-muted-foreground"
                      )}
                    >
                      {field.value
                        ? format(field.value, "dd.MM.yyyy")
                        : t("staff.fields.placeholders.selectDate")}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent align="start" className="p-0">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Person: Gender */}
        <FormField
          control={control}
          name="gender"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {t("staff.fields.gender")}
                <span className="text-main-red">*</span>
              </FormLabel>
              <FormControl>
                <RadioGroup
                  value={field.value}
                  onValueChange={field.onChange}
                  className="flex space-x-4"
                >
                  <label className="flex items-center space-x-2">
                    <RadioGroupItem value="Male" id="gender-m" />
                    <span>{t("staff.fields.male")}</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <RadioGroupItem value="Female" id="gender-f" />
                    <span>{t("staff.fields.female")}</span>
                  </label>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Person: Nationality */}
        <FormField
          control={control}
          name="nationality"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("staff.fields.nationality")}</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder={t("staff.fields.placeholders.nationality")}
                  className="h-11 bg-slate-200 border-transparent focus-visible:ring-blue-300"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <Button
        type="submit"
        size="lg"
        className="bg-slate-800"
        disabled={isLoading}
      >
        {isCreateForm ? t("submit") : t("staff.saveChanges")}
        <Send className="ml-2" />
      </Button>
    </BaseForm>
  );
}

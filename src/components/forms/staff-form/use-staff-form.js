import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { staffSchema } from "../user-form/user-schemas";
import { useEffect } from "react";
import { format } from "date-fns";
import { toast } from "sonner";

const mapToFormValues = (data) => ({
  job_title: data?.job_title || "",
  address: data?.address || "",
  hire_date: data?.hire_date ? new Date(data.hire_date) : null,
  department: data?.department || "",
  name: data?.person?.name || "",
  surname: data?.person?.surname || "",
  middle_name: data?.person?.middle_name || "",
  email: data?.person?.email || "",
  phone_number: data?.person?.phone_number || "",
  date_of_birth: data?.person?.date_of_birth
    ? new Date(data.person.date_of_birth)
    : null,
  gender: data?.person?.gender || "Male",
  nationality: data?.person?.nationality || "",
});

export function useStaffForm({ initialData, onSubmit }) {
  const form = useForm({
    resolver: zodResolver(staffSchema),
    defaultValues: mapToFormValues(initialData),
  });
  const { reset } = form;

  useEffect(() => {
    if (initialData) {
      reset(mapToFormValues(initialData));
    }
  }, [initialData, reset]);

  const handleSubmit = async (values) => {
    const payload = {
      job_title: values.job_title,
      address: values.address,
      hire_date: format(values.hire_date, "yyyy-MM-dd"),
      department: values.department,
      person: {
        name: values.name,
        surname: values.surname,
        middle_name: values.middle_name,
        email: values.email,
        phone_number: values.phone_number,
        date_of_birth: format(values.date_of_birth, "yyyy-MM-dd"),
        gender: values.gender,
        nationality: values.nationality,
      },
    };
    console.log(payload);
    try {
      await onSubmit(payload);
      !initialData?.id && toast.success("Staff Created!");
      reset();
    } catch (error) {
      toast.error(error.response.data?.detail || "Failed to create a staff");
    }
  };

  return { form, handleSubmit };
}

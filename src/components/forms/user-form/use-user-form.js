import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { studentSchema } from "./user-schemas";
import { format } from "date-fns";

const getDefaultValues = (data) => ({
  name: data?.person?.name || "",
  surname: data?.person?.surname || "",
  middle_name: data?.person?.middle_name || "",
  email: data?.person?.email || "",
  phone_number: data?.person?.phone_number || "",
  date_of_birth: data?.person?.date_of_birth
    ? new Date(data.person.date_of_birth)
    : null,
  gender: data?.person?.gender || "Male",
  nationality: data?.person?.nationality || "",
  profile_picture: data?.person?.profile_picture || "",
  graduation_year: data?.graduation_year || "",
  department: data?.department || "",
});

export function useUserForm({ initialData, onSubmit }) {
  const form = useForm({
    resolver: zodResolver(studentSchema),
    defaultValues: getDefaultValues(initialData),
  });

  // useEffect(() => {
  //   if (initialData) {
  //     reset(getDefaultValues(initialData));
  //   }
  // }, [initialData, reset]);

  const handleFormSubmit = async (values) => {
    // console.log(values);
    const payload = {
      department: values.department,
      person: {
        name: values.name,
        surname: values.surname,
        middle_name: values.middle_name,
        email: values.email,
        phone_number: values.phone_number,
        date_of_birth: format(values.date_of_birth, "yyyy-MM-dd"),
        gender: values.gender,
        nationality: values.nationality,
      },
    };
    console.log(payload);
    await onSubmit(values);

    // let finalOccupation = values.occupation;
    // const formData = new FormData();
    // formData.append("festival", values.festival);

    // try {
    //   await onSubmit(formData);
    //   hanldeOpenAlertDilog && hanldeOpenAlertDilog();
    //   reset();
    // } catch (error) {
    //   if (error instanceof AxiosError) {
    //     if (error.response?.status === 400) {
    //   const backendErrors = error.response?.data;
    //   // Iterate over backend error fields and set them
    //   Object.entries(backendErrors).forEach(([field, messages]) => {
    //     if (Array.isArray(messages)) {
    //       setError(field as keyof TSpeakerFormData, {
    //         message: messages.join(", "),
    //       });
    //     }
    //   });
  };

  return { form, handleFormSubmit };
}

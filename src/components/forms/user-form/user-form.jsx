import { Button } from "@/components/ui/button";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Calendar } from "@/components/ui/calendar";
import { Calendar as CalendarIcon, Send } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

import { BaseForm } from "../base-form";
import { useUserForm } from "./use-user-form";
import { cn, formatDate } from "@/lib/utils";
import { useTranslation } from "react-i18next";

export default function UserForm({
  isLoading = false,
  initialData = [],
  onSubmit,
}) {
  const { form, handleFormSubmit } = useUserForm({ initialData, onSubmit });
  const { control } = form;
  const { t } = useTranslation();
  return (
    <BaseForm
      form={form}
      onSubmit={handleFormSubmit}
      className="space-y-6 max-w-3xl mx-auto py-2 pb-8"
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* First Name Field */}
        <FormField
          control={control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel htmlFor="name">
                {t("firstName")}
                <span className="text-main-red">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  id="name"
                  type="text"
                  placeholder={t("placeholders.firstName")}
                  className="h-11 bg-slate-200 border-transparent focus-visible:ring-blue-300"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Last Name Field */}
        <FormField
          control={control}
          name="surname"
          render={({ field }) => (
            <FormItem>
              <FormLabel htmlFor="surname">
                {t("lastName")}
                <span className="text-main-red">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  id="surname"
                  type="text"
                  placeholder={t("placeholders.lastName")}
                  className="h-11 bg-slate-200 border-transparent focus-visible:ring-blue-300"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Middle Name Field */}
        <FormField
          control={control}
          name="middle_name"
          render={({ field }) => (
            <FormItem>
              <FormLabel htmlFor="middle_name">{t("middleName")}</FormLabel>
              <FormControl>
                <Input
                  id="middle_name"
                  type="text"
                  placeholder={t("placeholders.middleName")}
                  className="h-11 bg-slate-200 border-transparent focus-visible:ring-blue-300"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Email Field */}
        <FormField
          control={control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel htmlFor="email">
                Email<span className="text-main-red">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  className="h-11 bg-slate-200 border-transparent focus-visible:ring-blue-300"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Phone Number Field */}
        <FormField
          control={control}
          name="phone_number"
          render={({ field }) => (
            <FormItem>
              <FormLabel htmlFor="phone_number">{t("phoneNumber")}</FormLabel>
              <FormControl>
                <Input
                  id="phone_number"
                  type="tel"
                  placeholder="+1234567890"
                  className="h-11 bg-slate-200 border-transparent focus-visible:ring-blue-300"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Date of Birth Field using Popover and Calendar */}
        <FormField
          control={control}
          name="date_of_birth"
          render={({ field }) => (
            <FormItem className="">
              <FormLabel>
                {t("dateOfBirth")}
                <span className="text-main-red">*</span>
              </FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full h-11 bg-slate-200 hover:bg-slate-200 border-transparent focus-visible:ring-blue-300 font-normal",
                        !field.value && "text-muted-foreground"
                      )}
                    >
                      {field.value
                        ? formatDate(field.value, "en-ES")
                        : t("placeholders.selectDate")}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Gender Field */}
        <FormField
          control={control}
          name="gender"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {t("gender")}
                <span className="text-main-red">*</span>
              </FormLabel>
              <FormControl>
                <RadioGroup
                  value={field.value}
                  onValueChange={field.onChange}
                  className="flex space-x-4"
                >
                  <label className="flex items-center space-x-2">
                    <RadioGroupItem value="Male" id="gender-m" />
                    <span>{t("male")}</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <RadioGroupItem value="Female" id="gender-f" />
                    <span>{t("female")}</span>
                  </label>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Nationality Field */}
        <FormField
          control={control}
          name="nationality"
          render={({ field }) => (
            <FormItem>
              <FormLabel htmlFor="nationality">{t("nationality")}</FormLabel>
              <FormControl>
                <Input
                  id="nationality"
                  type="text"
                  placeholder={t("placeholders.nationality")}
                  className="h-11 bg-slate-200 border-transparent focus-visible:ring-blue-300"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Graduation Year Field */}
        <FormField
          control={control}
          name="graduation_year"
          render={({ field }) => (
            <FormItem>
              <FormLabel htmlFor="graduation_year">{t("gradYear")}</FormLabel>
              <FormControl>
                <Input
                  id="graduation_year"
                  type="number"
                  placeholder={t("placeholders.graduationYear")}
                  className="h-11 bg-slate-200 border-transparent focus-visible:ring-blue-300"
                  {...field}
                  onChange={(e) =>
                    field.onChange(e.target.value ? Number(e.target.value) : "")
                  }
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Department Field */}
        <FormField
          control={control}
          name="department"
          render={({ field }) => (
            <FormItem>
              <FormLabel htmlFor="department">{t("major")}</FormLabel>
              <FormControl>
                <Input
                  id="department"
                  type="text"
                  placeholder={t("placeholders.major")}
                  className="h-11 bg-slate-200 border-transparent focus-visible:ring-blue-300"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <Button
        type="submit"
        size="lg"
        className="bg-slate-800"
        disabled={isLoading}
      >
        {t("register")}
        <Send className="size-6 ml-2" />
      </Button>
    </BaseForm>
  );
}

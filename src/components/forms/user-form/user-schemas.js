import { z } from "zod";

export const personSchema = z.object({
  name: z.string().min(1, { message: "Name is required" }),
  surname: z.string().min(1, { message: "Surname is required" }),
  middle_name: z.string().optional(),
  email: z.string().email({ message: "Invalid email address" }),
  phone_number: z.string().optional(),
  date_of_birth: z.date().optional(),
  gender: z.enum(["Male", "Female"], { message: "Gender is required" }),
  nationality: z.string().optional(),
  profile_picture: z.string().optional(),
});

export const studentSchema = personSchema.extend({
  graduation_year: z.number().optional(),
  department: z.string().optional(),
});

export const staffSchema = personSchema.extend({
  job_title: z.string().min(1, { message: "Job title is required" }),
  address: z.string().min(1, { message: "Address is required" }),
  hire_date: z.date().optional(),
  department: z.string().min(1, { message: "Department is required" }),
});

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Check, ChevronsUpDown } from "lucide-react";
import { useDebounce } from "@/hooks/use-debounce";
import { useStaff } from "@/api/staff/queries";
import { ComboboxSearch } from "./combobox-search";

export function ManualRecordDialog({
  triggerButton,
  isRecordPending = false,
  onRecord,
}) {
  const [query, setQuery] = useState("");
  const [selectedId, setSelectedId] = useState(null);

  const debounced = useDebounce(query, 320);

  const {
    data: staffList = [],
    isLoading,
    isError,
  } = useStaff({
    skip: 0,
    pageSize: 50,
    search: debounced,
    on_campus: "",
    department: "",
  });

  const options = staffList.map((entry) => ({
    value: entry.person_id,
    label: `${entry.person.name} ${entry.person.surname}`,
  }));

  return (
    <Dialog>
      <DialogTrigger asChild>{triggerButton}</DialogTrigger>
      <DialogContent className="sm:max-w-[425px] overflow-visible">
        <DialogHeader>
          <DialogTitle>Manual Record</DialogTitle>
          <DialogDescription>
            Search your staff and record Entry or Exit.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <ComboboxSearch
            value={selectedId}
            onChange={setSelectedId}
            query={query}
            onQueryChange={setQuery}
            options={options}
            loading={isLoading}
            error={isError}
            placeholder="Search staff..."
            label="Select staff..."
          />
          <div className="flex justify-around mt-4">
            <Button
              disabled={!selectedId || isRecordPending}
              className="bg-emerald-600 hover:bg-emerald-500 w-24"
              onClick={() => onRecord({ personId: selectedId, action: "in" })}
            >
              Entry
            </Button>
            <Button
              disabled={!selectedId || isRecordPending}
              className="bg-red-600 hover:bg-red-500 w-24"
              onClick={() => onRecord({ personId: selectedId, action: "out" })}
            >
              Exit
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

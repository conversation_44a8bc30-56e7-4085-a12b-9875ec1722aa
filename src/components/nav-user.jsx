import { BellIcon, LogOutIcon, UserCircleIcon } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "./ui/button";
import LogoutButton from "./auth/logout/logout-button";
import { getUser } from "@/store/auth/auth-store";
import { useCurrentUser } from "@/api/auth/queries";

export function NavUser() {
  // const {} = useCurrentUser();
  const { person: user } = getUser();

  const navUser = {
    name: user?.name + " " + user?.surname,
    role: user?.person?.role || "superuser",
    avatar:
      user?.profile_picture ||
      "https://i.pinimg.com/736x/40/98/2a/40982a8167f0a53dedce3731178f2ef5.jpg",
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button className="h-9 w-9 rounded-full overflow-hidden">
          <Avatar className="h-9 w-9">
            <AvatarImage src={navUser.avatar} alt={navUser.name} />
            <AvatarFallback className="rounded-lg">??</AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
        side={"bottom"}
        align="end"
        sideOffset={4}
      >
        <DropdownMenuLabel className="p-0 font-normal">
          <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
            <Avatar className="h-8 w-8 rounded-lg">
              <AvatarImage src={navUser.avatar} alt={navUser.name} />
              <AvatarFallback className="rounded-lg">CN</AvatarFallback>
            </Avatar>
            <div className="grid flex-1 text-left text-sm leading-tight">
              <span className="truncate font-medium">{navUser.name}</span>
              <span className="truncate text-xs text-muted-foreground">
                {navUser.role}
              </span>
            </div>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem>
            <UserCircleIcon />
            Account
          </DropdownMenuItem>
          <DropdownMenuItem>
            <BellIcon />
            Notifications
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem>
          <LogoutButton className="w-full bg-transparent text-foreground hover:bg-transparent p-0 h-auto font-normal justify-start">
            <LogOutIcon />
            Log out
          </LogoutButton>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

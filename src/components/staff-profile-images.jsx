import { useStaffImagesById } from "@/api/staff/queries";
import ImagesList from "./ImageList";

const StaffProfileImages = ({ id }) => {
  const { data, isLoading, error } = useStaffImagesById(id);

  if (isLoading) {
    return <div>Loading...</div>;
  }
  if (error || !data) {
    return <div>{error.response.data.detail}</div>;
  }

  const images = data.map((item) => {
    return {
      id: item.id,
      image: item.image,
      trained: item.trained,
    };
  });
  return <ImagesList images={images} />;
};

export default StaffProfileImages;

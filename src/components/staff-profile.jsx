import { format } from "date-fns";
import Placeholder from "@/assets/placeholder.jpg";
import { useTranslation } from "react-i18next";
import DeleteStaffButton from "./staff/staff-delete-button";
import { getProfileImage } from "@/lib/utils";
import { Link } from "@tanstack/react-router";

const StaffProfile = ({ data }) => {
  const { t } = useTranslation();

  const {
    id: staffId,
    job_title,
    address,
    hire_date,
    department,
    person,
  } = data;
  const {
    name,
    surname,
    middle_name,
    date_of_birth,
    gender,
    email,
    phone_number,
    nationality,
    profile_picture,
  } = person;

  const fullName = [name, middle_name, surname].filter(Boolean).join(" ");

  return (
    <div className="flex-1">
      <img
        src={getProfileImage(profile_picture) || Placeholder}
        alt={fullName}
        className="w-32 h-32 rounded-full mx-auto mb-4 object-cover"
      />
      <h2 className="text-xl font-semibold text-center">{fullName}</h2>
      <p className="text-gray-600 text-center">{email}</p>

      <div className="mt-4 space-y-2 flex flex-col items-start">
        <p>
          <strong className="text-sm text-gray-600">
            {t("staff.fields.department")}:
          </strong>{" "}
          {department}
        </p>
        <p>
          <strong className="text-sm text-gray-600">
            {t("staff.fields.jobTitle")}:
          </strong>{" "}
          {job_title}
        </p>
        <p>
          <strong className="text-sm text-gray-600">
            {t("staff.fields.phoneNumber")}:
          </strong>{" "}
          {phone_number}
        </p>
        <p>
          <strong className="text-sm text-gray-600">
            {t("staff.fields.gender")}:
          </strong>{" "}
          {gender || "-"}
        </p>
        <p>
          <strong className="text-sm text-gray-600">
            {t("staff.fields.nationality")}:
          </strong>{" "}
          {nationality || "-"}
        </p>
        <p>
          <strong className="text-sm text-gray-600">
            {t("staff.fields.address")}:
          </strong>{" "}
          {address}
        </p>
        <p>
          <strong className="text-sm text-gray-600">
            {t("staff.fields.hireDate")}:
          </strong>{" "}
          {hire_date ? format(new Date(hire_date), "dd.MM.yyyy") : "-"}
        </p>
        <p>
          <strong className="text-sm text-gray-600">
            {t("staff.fields.dateOfBirth")}:
          </strong>{" "}
          {date_of_birth ? format(new Date(date_of_birth), "dd.MM.yyyy") : "-"}
        </p>
      </div>

      <div className="mt-4 flex justify-end gap-2">
        <Link
          to={`edit`}
          className="bg-gray-400 text-white px-4 py-2 rounded-md"
        >
          {t("edit")}
        </Link>
        <DeleteStaffButton id={staffId} />
      </div>
    </div>
  );
};

export default StaffProfile;

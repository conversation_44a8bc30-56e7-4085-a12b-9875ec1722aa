import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import { formatEntryExitTime } from "@/lib/formatDates";
import { cn, getAvatarInitials } from "@/lib/utils";
import { Link } from "@tanstack/react-router";
import { ArrowUpDown, MoreHorizontal } from "lucide-react";
import { useTranslation } from "react-i18next";

export const STAFF_RECORDS_COLUMNS = [
  {
    header: "Logo",
    cell: ({ row }) => (
      <div className="min-h-10 min-w-10 w-10 h-10 rounded-full border-admin-primary border flex justify-center items-center font-medium text-violet-600 bg-violet-50 border-violet-600">
        {getAvatarInitials(row.original.name)}
      </div>
    ),
  },
  {
    accessorKey: "name",
    header: ({ column }) => {
      const { t } = useTranslation();
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t("name")}
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: "phone_number",
    header: () => {
      const { t } = useTranslation();
      return t("phoneNumber");
    },
  },
  {
    accessorKey: "timestamp",
    header: () => {
      const { t } = useTranslation();
      return t("dateTime");
    },
    cell: ({ row }) => {
      const date = new Date(row.original.timestamp);
      const formattedDate = formatEntryExitTime(date);
      return <span>{formattedDate}</span>;
    },
  },
  {
    accessorKey: "action",
    header: "Action",
    cell: ({ row }) => {
      const { t } = useTranslation();
      const isEntered = row.original.action === "in";
      const label = isEntered ? t("records.entered") : t("records.exited");
      const variantColor = isEntered ? "text-green-500" : "text-red-500";
      return (
        <div
          className={cn(
            "w-full text-sm text-start font-medium py-0.5 px-2 rounded-lg",
            variantColor
          )}
        >
          {label}
        </div>
      );
    },
  },
  {
    accessorKey: "method",
    header: () => {
      return <span className="text-sm">Method</span>;
    },
  },
  {
    accessorKey: "made_by",
    header: () => {
      const { t } = useTranslation();
      return t("madeBy");
    },
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const { t } = useTranslation();
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">{t("openMenu")}</span>
              <MoreHorizontal />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>{t("actions")}</DropdownMenuLabel>
            <Link to={`/dashboard/staffs/${row.original.staff_id}`}>
              <DropdownMenuItem className="cursor-pointer">
                {t("viewDetail")}
              </DropdownMenuItem>
            </Link>
            {/* <Link href={`${row.original.id}/edit`}>
              <DropdownMenuItem>{t("edit")}</DropdownMenuItem>
            </Link> */}
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];

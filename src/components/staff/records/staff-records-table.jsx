import { useStaffRecords } from "@/api/staff/queries";
import { DataTableContainer } from "@/components/user-tables/data-table-container";
import { STAFF_RECORDS_COLUMNS } from "./columns";
import { useFilter } from "@/hooks/use-filter";

const StaffRecordsTable = ({ routeId }) => {
  const { searchParams, setSearchParams } = useFilter(routeId);
  const { page, pageSize } = searchParams;
  const skip = (page - 1) * pageSize;

  const { data, isLoading, error } = useStaffRecords({ skip, pageSize });

  if (isLoading) return <div>Loading...</div>;

  if (error || !data) return <pre>{JSON.stringify(error, null, 2)}</pre>;

  const tableData = data.map((item) => ({
    id: item.id,
    staff_id: item?.entity_id,
    name: item.person?.name + " " + item.person?.surname,
    action: item.action,
    method: item.method,
    timestamp: item.timestamp,
    phone_number: item.person?.phone_number,
    made_by_id: item.security_person_id,
    made_by: item.security_person?.name + " " + item.security_person?.surname,
  }));

  return (
    <DataTableContainer
      columns={STAFF_RECORDS_COLUMNS}
      data={tableData}
      includeToolbar={false}
      includePagination={true}
      pageIndex={page - 1}
      pageSize={pageSize}
      pageSizeOptions={[10, 20, 50]}
      onPaginationChange={({ pageIndex, pageSize }) => {
        setSearchParams({ page: pageIndex + 1, pageSize });
      }}
    />
  );
};

export default StaffRecordsTable;

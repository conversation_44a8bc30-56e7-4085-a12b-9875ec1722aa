import { useStaffSelfRecords } from "@/api/staff/queries";
import { DataTableContainer } from "@/components/user-tables/data-table-container";
import { STAFF_SELF_RECORDS_COLUMNS } from "./columns";

const StaffSelfRecordsTable = ({ personId }) => {
  const { data, isLoading, error } = useStaffSelfRecords(personId);

  if (isLoading) return <div>Loading...</div>;

  if (error || !data) return <pre>{JSON.stringify(error, null, 2)}</pre>;

  const tableData = data.map((item) => ({
    id: item.id,
    staff_id: item?.person?.id,
    name: item.person?.name + " " + item.person?.surname,
    action: item.action,
    timestamp: item.timestamp,
    made_by: item.security_person?.name + " " + item.security_person?.surname,
  }));

  return (
    <DataTableContainer
      columns={STAFF_SELF_RECORDS_COLUMNS}
      data={tableData}
      includeToolbar={false}
    />
  );
};

export default StaffSelfRecordsTable;

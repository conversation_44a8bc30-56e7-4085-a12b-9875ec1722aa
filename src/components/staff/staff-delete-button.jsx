import { useDeleteStaff } from "@/api/staff/queries";
import { useNavigate } from "@tanstack/react-router";
import ConfirmDialog from "../confirm-dialog";
import { Button } from "../ui/button";
import { useTranslation } from "react-i18next";

const DeleteStaffButton = ({ id }) => {
  const { mutate: deleteStaff, isPending } = useDeleteStaff();
  const { t } = useTranslation();
  const navigate = useNavigate();

  const handleDeleteFestival = () => {
    deleteStaff(id);
    navigate({ to: "/dashboard/staffs" });
  };

  return (
    <ConfirmDialog
      title="Удалить?"
      description="Это действие невозможно отменить. Вы уверены, что хотите продолжить?"
      confirmText="Удалить"
      cancelText="Отмена"
      onConfirm={() => handleDeleteFestival()}
      triggerElement={
        <Button size={"lg"} variant="destructive" disabled={isPending}>
          {isPending ? t("button.deleting") : t("button.delete")}
        </Button>
      }
    />
  );
};

export default DeleteStaffButton;

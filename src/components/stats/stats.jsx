import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ontent } from "@/components/ui/card";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
} from "@/components/ui/chart";
import { <PERSON><PERSON>hart, Bar, CartesianGrid, XAxis, <PERSON><PERSON>hart, Line } from "recharts";
import {useTranslation} from "react-i18next";

// Data for Total Entries/Exits chart
const totalEntriesData = [
  { period: "Daily", Entries: 150, Exits: 140 },
  { period: "Weekly", Entries: 1025, Exits: 980 },
  { period: "Monthly", Entries: 4100, Exits: 3950 },
];

const totalChartConfig = {
  Entries: {
    label: "Entries",
    color: "hsl(var(--chart-1))",
  },
  Exits: {
    label: "Exits",
    color: "hsl(var(--chart-2))",
  },
};

// Data for Peak Hours chart
const peakHoursData = [
  { hour: "6 AM", Entries: 5, Exits: 4 },
  { hour: "7 AM", Entries: 20, Exits: 15 },
  { hour: "8 AM", Entries: 50, Exits: 45 },
  { hour: "9 AM", Entries: 80, Exits: 70 },
  { hour: "10 AM", Entries: 120, Exits: 110 },
  { hour: "11 AM", Entries: 150, Exits: 140 },
  { hour: "12 PM", Entries: 200, Exits: 190 },
  { hour: "1 PM", Entries: 220, Exits: 210 },
  { hour: "2 PM", Entries: 180, Exits: 170 },
  { hour: "3 PM", Entries: 140, Exits: 130 },
  { hour: "4 PM", Entries: 100, Exits: 90 },
  { hour: "5 PM", Entries: 60, Exits: 50 },
  { hour: "6 PM", Entries: 30, Exits: 25 },
  { hour: "7 PM", Entries: 20, Exits: 15 },
  { hour: "8 PM", Entries: 10, Exits: 5 },
];

// Chart config for peak hours
const peakHoursChartConfig = {
  Entries: {
    label: "Entries",
    color: "hsl(var(--chart-1))",
  },
  Exits: {
    label: "Exits",
    color: "hsl(var(--chart-2))",
  },
};

export default function StatsPage() {
  const { t } = useTranslation();
  return (
    <div className="max-container mx-auto p-4 space-y-6">
      <h1 className="text-2xl font-medium mb-4">{t('appSidebar.data.statistics')}</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Card for Total Entries/Exits */}
        <Card>
          <CardHeader>
            <CardTitle>{t('totalEntries')}</CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={totalChartConfig}
              className="min-h-[300px] w-full"
            >
              <BarChart data={totalEntriesData}>
                <CartesianGrid vertical={false} />
                <XAxis
                  dataKey="period"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={10}
                />
                <ChartTooltip
                  content={<ChartTooltipContent labelKey="period" />}
                />
                <ChartLegend content={<ChartLegendContent />} />
                <Bar dataKey="Entries" fill="var(--color-Entries)" radius={4} />
                <Bar dataKey="Exits" fill="var(--color-Exits)" radius={4} />
              </BarChart>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Card for Peak Hours */}
        <Card>
          <CardHeader>
            <CardTitle>{t('peakHours')}</CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={peakHoursChartConfig}
              className="min-h-[300px] w-full"
            >
              <LineChart data={peakHoursData}>
                <CartesianGrid vertical={false} />
                <XAxis
                  dataKey="hour"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={10}
                  tickFormatter={(value) => value}
                />
                <ChartTooltip
                  content={<ChartTooltipContent labelKey="hour" />}
                />
                <ChartLegend content={<ChartLegendContent />} />
                <Line
                  type="monotone"
                  dataKey="Entries"
                  stroke="var(--color-Entries)"
                  strokeWidth={2}
                  dot={{ r: 3 }}
                />
                <Line
                  type="monotone"
                  dataKey="Exits"
                  stroke="var(--color-Exits)"
                  strokeWidth={2}
                  dot={{ r: 3 }}
                />
              </LineChart>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

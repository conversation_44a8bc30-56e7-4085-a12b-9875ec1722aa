import { useStudentImagesById } from "@/api/students/queries";
import ImagesList from "./ImageList";

const StudentProfileImages = ({ id }) => {
  const { data, isLoading, error } = useStudentImagesById(id);

  if (isLoading) {
    return <div>Loading...</div>;
  }
  if (error || !data) {
    return <div>{error.response.data.detail}</div>;
  }

  const images = data.map((item) => {
    return {
      id: item.id,
      image: item.image,
      trained: item.trained,
    };
  });
  return <ImagesList images={images} />;
};

export default StudentProfileImages;

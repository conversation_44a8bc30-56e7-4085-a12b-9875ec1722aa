// import Placeholder from "@/assets/placeholder.jpg";
import { useTranslation } from "react-i18next";
import { format } from "date-fns";
import DeleteStudentButton from "./student/student-delete-button";
import StudentAvatar from "./student/student-avatar";

const StudentProfile = ({ data }) => {
  const { t } = useTranslation();

  const {
    id: studentId,
    graduation_year,
    department,
    on_campus,
    person,
  } = data;
  const {
    id: personId,
    name,
    surname,
    middle_name,
    date_of_birth,
    gender,
    email,
    phone_number,
    nationality,
    //profile_picture,
  } = person;
  const fullName = [name, middle_name, surname].filter(Boolean).join(" ");

  return (
    <div className="flex-1 rounded-md border bg-white p-4 sm:p-6">
      <div className="flex justify-center mb-4">
        <StudentAvatar personId={personId} />
      </div>

      <h2 className="text-xl font-semibold text-center">{fullName}</h2>
      <p className="text-gray-600 text-center">{email}</p>
      <div className="mt-4 space-y-2 flex flex-col items-start">
        <p>
          <strong className="text-sm text-gray-600">{t("phone")}:</strong>{" "}
          {phone_number || "-"}
        </p>
        <p>
          <strong className="text-sm text-gray-600">{t("gender")}:</strong>{" "}
          {gender || "-"}
        </p>
        <p>
          <strong className="text-sm text-gray-600">
            {t("staff.fields.nationality")}:
          </strong>{" "}
          {nationality || "-"}
        </p>
        <p>
          <strong className="text-sm text-gray-600">
            {t("staff.fields.dateOfBirth")}:
          </strong>{" "}
          {date_of_birth ? format(new Date(date_of_birth), "dd.MM.yyyy") : "-"}
        </p>
        <p>
          <strong className="text-sm text-gray-600">{t("major")}:</strong>{" "}
          {department || "-"}
        </p>
        <p>
          <strong className="text-sm text-gray-600">{t("gradYear")}:</strong>{" "}
          {graduation_year || "-"}
        </p>
        <div className="flex items-center gap-3">
          <strong className="text-sm text-gray-600">{t("onCampStat")}:</strong>{" "}
          {on_campus ? (
            <div className="w-max text-sm text-center font-medium py-1 px-2 rounded-lg bg-green-100 text-green-500">
              {t("onCampus")}
            </div>
          ) : (
            <div className="w-max text-sm text-center font-medium py-1 px-2 rounded-lg bg-red-100 text-red-500">
              {t("offCampus")}
            </div>
          )}
        </div>
      </div>
      <div className="mt-4 flex justify-end gap-2">
        <DeleteStudentButton id={studentId} />
        {/* <button className="bg-gray-400 text-white px-4 py-2 rounded-md">
          {t("edit")}
        </button>
        <button className="bg-red-600 text-white px-4 py-2 rounded-md">
          {t("delete")}
        </button> */}
      </div>
    </div>
  );
};
export default StudentProfile;

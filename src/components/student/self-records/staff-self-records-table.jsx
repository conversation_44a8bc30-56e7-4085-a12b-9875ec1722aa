import { DataTableContainer } from "@/components/user-tables/data-table-container";
import { STUDENT_SELF_RECORDS_COLUMNS } from "./columns";
import { useStudentSelfRecords } from "@/api/students/queries";

const StudentSelfRecordsTable = ({ personId }) => {
  const { data, isLoading, error } = useStudentSelfRecords(personId);

  if (isLoading) return <div>Loading...</div>;

  if (error || !data) return <pre>{JSON.stringify(error, null, 2)}</pre>;

  const tableData = data.map((item) => ({
    id: item.id,
    staff_id: item?.person?.data?.id,
    name: item?.person.name + " " + item?.person.surname,
    action: item.action,
    timestamp: item.timestamp,
    // phone_number: item.person?.person?.phone_number,
    made_by: item?.security_person.name + " " + item?.security_person.surname,
  }));

  return (
    <DataTableContainer
      columns={STUDENT_SELF_RECORDS_COLUMNS}
      data={tableData}
      includeToolbar={false}
    />
  );
};

export default StudentSelfRecordsTable;

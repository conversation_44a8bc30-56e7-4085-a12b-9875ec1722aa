import { useStudentProfileImage } from "@/api/students/queries";

export default function StudentAvatar({ personId }) {
  const {
    data: imageUrl,
    isLoading,
    isError,
  } = useStudentProfileImage(personId);

  if (isLoading)
    return <div className="w-24 h-24 rounded-full bg-violet-50">...</div>;
  if (isError)
    return <div className="w-24 h-24 rounded-full bg-violet-50">Error</div>;

  return (
    <img
      src={imageUrl || ""}
      alt="Profile"
      className="w-24 h-24 rounded-full object-cover"
    />
  );
}

import { useNavigate } from "@tanstack/react-router";
import ConfirmDialog from "../confirm-dialog";
import { Button } from "../ui/button";
import { useTranslation } from "react-i18next";
import { useDeleteStudent } from "@/api/students/queries";

const DeleteStudentButton = ({ id }) => {
  const { mutate: deleteStudent, isPending } = useDeleteStudent();
  const { t } = useTranslation();
  const navigate = useNavigate();

  const handleDeleteFestival = () => {
    deleteStudent(id);
    navigate({ to: "/dashboard/students" });
  };

  return (
    <ConfirmDialog
      title="Удалить?"
      description="Это действие невозможно отменить. Вы уверены, что хотите продолжить?"
      confirmText="Удалить"
      cancelText="Отмена"
      onConfirm={() => handleDeleteFestival()}
      triggerElement={
        <Button size={"lg"} variant="destructive" disabled={isPending}>
          {isPending ? t("button.deleting") : t("button.delete")}
        </Button>
      }
    />
  );
};

export default DeleteStudentButton;

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useTranslation } from "react-i18next";
import { useState, useEffect } from "react";
import { Globe } from "lucide-react";

const LANGUAGES = {
  en: "EN",
  ru: "RU",
};

const Switcher = () => {
  const { t, i18n } = useTranslation();
  const [lang, setLang] = useState(i18n.language || "en");

  useEffect(() => {
    const storedLang = localStorage.getItem("lang");
    if (storedLang && storedLang !== lang) {
      i18n.changeLanguage(storedLang);
      setLang(storedLang);
    }
  }, []);

  const handleLanguageChange = (lng) => {
    i18n.changeLanguage(lng);
    localStorage.setItem("lang", lng);
    setLang(lng);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger className="text-sm px-3 py-1 rounded-md border bg-white hover:bg-slate-100 flex items-center gap-1">
        <Globe /> <span>{LANGUAGES[lang]}</span>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => handleLanguageChange("en")}>
          EN {t("english")}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleLanguageChange("ru")}>
          RU {t("russian")}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default Switcher;

import { useForm } from "react-hook-form";
import { Input } from "@/components/ui/input.jsx";
import { Button } from "@/components/ui/button";
import {useTranslation} from "react-i18next";

const PasswordUpdateForm = ({ onSubmit, isLoading }) => {
    const { t } = useTranslation();
    const {
        register,
        handleSubmit,
        watch,
        formState: { errors },
    } = useForm();

    return (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 max-w-md mx-auto">
            <h1 className="text-center text-2xl font-semibold text-slate-800 mb-3.5">{t('changePassword')}</h1>
            <div>
                <label className="block text-sm font-medium text-gray-700">{t('currentPassword')}</label>
                <Input type="password"
                    {...register("old_password", { required: t('currentPasswordRequired') })}
                       className="focus-visible:ring-0"
                />
                {errors.old_password && (
                    <p className="text-red-500 text-xs mt-1">{errors.old_password.message}</p>
                )}
            </div>
            <div>
                <label className="block text-sm font-medium text-gray-700">{t('newPassword')}</label>
                <Input
                    type="password"
                    {...register("new_password", { required: t('newPasswordRequired') })}
                    className="focus-visible:ring-0"
                />
                {errors.new_password && (
                    <p className="text-red-500 text-xs mt-1">{errors.new_password.message}</p>
                )}
            </div>
            <div>
                <label className="block text-sm font-medium text-gray-700">{t('confirmPassword')}</label>
                <Input className="focus-visible:ring-0"
                    type="password"
                    {...register("confirmNewPassword", {
                        required: t('confirmPassword'),
                        validate: (value) => value === watch("new_password") || t('notMatch')
                    })}
                />
                {errors.confirmNewPassword && (
                    <p className="text-red-500 text-xs mt-1">{errors.confirmNewPassword.message}</p>
                )}
            </div>
            <Button type="submit" className="w-full bg-slate-800">{t('change')}</Button>
        </form>
    )
};
export default PasswordUpdateForm;
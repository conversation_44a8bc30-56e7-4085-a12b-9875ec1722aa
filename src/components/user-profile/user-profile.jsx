import Placeholder from "@/assets/placeholder.jpg";

const UserProfile = ({userData}) => {
    return (
        <div className="flex-1 rounded-md border bg-white p-4 sm:p-6">
            <img
                src={userData.avatar ? userData.avatar : Placeholder}
                alt={userData.username}
                className="w-32 h-32 rounded-full mx-auto mb-1 object-cover"
            />
            <p className='text-sm text-gray-600 text-center mb-1'>{userData.role}</p>
            <h2 className='text-xl font-semibold text-center'>{userData.username}</h2>
            <p className='text-gray-600 text-center'>{userData.name}</p>
            {/*<strong className='text-gray-600'></strong>*/}
        </div>
    )
};
export default UserProfile;
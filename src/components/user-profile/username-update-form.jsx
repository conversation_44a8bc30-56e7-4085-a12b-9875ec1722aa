import { useForm } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {useTranslation} from "react-i18next";

const UsernameUpdateForm = ({ onSubmit, isLoading }) => {
  const { t } = useTranslation();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm();

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="space-y-4 max-w-md mx-auto"
    >
      <h1 className="text-center text-2xl font-semibold text-slate-800 mb-3.5">
        {t('changeUsername')}
      </h1>
      <div>
        <label className="block text-sm font-medium text-gray-700">
          {t('newUsername')}
        </label>
        <Input
          type="text"
          {...register("new_username", { required: t('usernameRequired') })}
          className="focus-visible:ring-0"
        />
        {errors.new_username && (
          <p className="text-red-500 text-xs mt-1">
            {errors.new_username.message}
          </p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">
          {t('currentPassword')}
        </label>
        <Input
          type="password"
          {...register("current_password", { required: t('passwordRequired') })}
          className="focus-visible:ring-0"
        />
        {errors.current_password && (
          <p className="text-red-500 text-xs mt-1">
            {errors.current_password.message}
          </p>
        )}
      </div>

      <Button type="submit" className="w-full bg-slate-800">
        {isLoading ? t("loading") : t("change")}
      </Button>
    </form>
  );
};
export default UsernameUpdateForm;

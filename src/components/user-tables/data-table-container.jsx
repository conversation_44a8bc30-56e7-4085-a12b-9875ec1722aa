import {
  useReactTable,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
} from "@tanstack/react-table";
import DataTableToolbar from "./data-table-toolbar";
import { DataTable } from "../ui/data-table";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";

export function DataTableContainer({
  columns,
  data,
  includeToolbar = false,
  includePagination = false,
  DownloadExcelBtn,
  searchPlaceholder = "Search …",
  newLink = "new",
  newButtonLabel = "+ Create",
  type = "student",
  searchParams,
  setSearchParams,
  pageIndex,
  pageSize,
  onPaginationChange,
  pageSizeOptions = [10, 20, 50],
}) {
  const table = useReactTable({
    data: data,
    columns,
    manualPagination: true,
    state: { pagination: { pageIndex, pageSize } },
    onPaginationChange,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  return (
    <>
      {includeToolbar && (
        <DataTableToolbar
          searchParams={searchParams}
          setSearchParams={setSearchParams}
          searchPlaceholder={searchPlaceholder}
          newLink={newLink}
          type={type}
          DownloadExcelBtn={DownloadExcelBtn}
          newButtonLabel={newButtonLabel}
        />
      )}

      <DataTable table={table} columns={columns} />

      {includePagination && (
        <div className="flex items-center justify-end space-x-2 py-4">
          <span>
            [{pageIndex * pageSize} - {(pageIndex + 1) * pageSize}]
          </span>
          <Button
            size="sm"
            variant={"outline"}
            className="px-4 h-10"
            disabled={pageIndex === 0}
            onClick={() =>
              onPaginationChange({ pageIndex: pageIndex - 1, pageSize })
            }
          >
            {"<"}
          </Button>
          <Button
            size="sm"
            variant={"outline"}
            className="px-4 h-10"
            disabled={data.length <= 0}
            onClick={() =>
              onPaginationChange({ pageIndex: pageIndex + 1, pageSize })
            }
          >
            {">"}
          </Button>
          <Select
            value={String(pageSize)}
            onValueChange={(val) =>
              onPaginationChange({ pageIndex: 0, pageSize: Number(val) })
            }
            className="w-[80px]"
          >
            <SelectTrigger className="w-[80px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {pageSizeOptions.map((opt) => (
                <SelectItem key={opt} value={String(opt)}>
                  {opt}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}
    </>
  );
}

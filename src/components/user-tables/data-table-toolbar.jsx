import { Input } from "@/components/ui/input.jsx";
import { Link } from "@tanstack/react-router";
import { Button } from "@/components/ui/button.jsx";
import { useEffect, useState } from "react";
import { useDebounce } from "@/hooks/use-debounce";
import CampusFilterSelect from "./staffs-table/CampusStatusFilter";
import DepartmentFilterSelect from "./staffs-table/DepartmentFilterSelect";

const staffDepartmentOptions = [
  { value: "-", label: "All Departments" },
  { value: "Housekeeping", label: "Housekeeping" },
  { value: "MEP", label: "MEP" },
  { value: "Security", label: "Security" },
  { value: "Canteen", label: "Canteen" },
  { value: "IT", label: "IT" },
  { value: "Finance", label: "Finance" },
  { value: "Human Resources", label: "Human Resources" },
  { value: "UCA", label: "UCA" },
  { value: "Operations", label: "Operations" },
  { value: "Administaration", label: "Administaration" },
  { value: "MMD", label: "MMD" },
  { value: "Operations/Administartion", label: "Operations/Administartion" },
];

const studentDepartmentOptions = [
  { value: "-", label: "All Majors" },
  { value: "Computer Science", label: "Computer Science" },
  { value: "Communication and Media", label: "Communication and Media" },
];
const campusStatusOptions = [
  { value: "-", label: "All campus" },
  { value: "true", label: "On campus" },
  { value: "false", label: "Off campus" },
];

export function DataTableToolbar({
  searchPlaceholder = "Search ...",
  newLink = "new",
  newButtonLabel = "Create",
  type = "student",
  searchParams,
  DownloadExcelBtn,
  setSearchParams,
}) {
  const [localSearch, setLocalSearch] = useState(searchParams.search || "");
  const debouncedSearch = useDebounce(localSearch, 500);

  useEffect(() => {
    if (debouncedSearch !== searchParams.search) {
      setSearchParams({ page: 1, search: debouncedSearch });
    }
  }, [debouncedSearch, searchParams.search, setSearchParams]);

  return (
    <div className="flex items-center justify-between flex-wrap gap-4 pb-4">
      <div className="flex items-center  gap-2">
        <Input
          placeholder={searchPlaceholder}
          value={localSearch}
          onChange={(e) => setLocalSearch(e.target.value)}
          className="max-w-sm bg-white focus-visible:ring-admin-primary"
        />

        <CampusFilterSelect
          options={campusStatusOptions}
          value={searchParams.on_campus || "-"}
          onValueChange={(value) => {
            setSearchParams({ on_campus: value === "-" ? "" : value });
          }}
        />

        <DepartmentFilterSelect
          value={searchParams.department || "-"}
          options={
            type === "student"
              ? studentDepartmentOptions
              : staffDepartmentOptions
          }
          onValueChange={(value) => {
            setSearchParams({ department: value === "-" ? "" : value });
          }}
        />
      </div>
      <div className="flex items-center gap-2">
        {DownloadExcelBtn && <DownloadExcelBtn />}
        <Link to={newLink}>
          <Button className="bg-slate-700 hover:bg-slate-700/90">
            {newButtonLabel}
          </Button>
        </Link>
      </div>
    </div>
  );
}

export default DataTableToolbar;

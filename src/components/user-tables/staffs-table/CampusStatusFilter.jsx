import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";

export default function CampusFilterSelect({
  options = [],
  value,
  onValueChange,
}) {
  return (
    <Select
      //  value={value}
      defaultValue={value}
      onValueChange={onValueChange}
    >
      <SelectTrigger className="w-48">
        <SelectValue placeholder="Campus filter" />
      </SelectTrigger>
      <SelectContent>
        {options.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            {option.label}
          </SelectItem>
        ))}
        {/* <SelectItem value={"-"}>Campus Status</SelectItem>
        <SelectItem value={"true"}>On campus</SelectItem>
        <SelectItem value={"false"}>Off campus</SelectItem> */}
      </SelectContent>
    </Select>
  );
}

import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";

export default function DepartmentFilterSelect({
  options = [],
  value,
  onValueChange,
}) {
  return (
    <Select
      // defaultValue={value !== "" ? value : undefined}
      // value={value !== "" ? value : undefined}
      value={value}
      onValueChange={onValueChange}
    >
      <SelectTrigger className="w-48">
        <SelectValue placeholder="Department filter" />
      </SelectTrigger>
      <SelectContent>
        {options.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

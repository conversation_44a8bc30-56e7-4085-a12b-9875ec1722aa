import { useState } from "react";
import { Download } from "lucide-react";
import { Button } from "@/components/ui/button";
import { getStaffExcel } from "@/api/staff/fetchers";
import { toast } from "sonner";

export default function DownloadExcelBtn() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleDownload = async () => {
    setLoading(true);
    setError(null);
    try {
      // fetch the Excel blob
      const blob = await getStaffExcel();
      // create a blob URL
      const url = window.URL.createObjectURL(new Blob([blob]));
      // create a temporary link to trigger download
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "staff_export.xlsx");
      document.body.appendChild(link);
      link.click();
      link.parentNode.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (e) {
      console.error("Download failed", e);
      toast.error("Download failed. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button
      variant="outline"
      className="bg-white hover:bg-white/90 flex items-center gap-2"
      onClick={handleDownload}
      disabled={loading}
    >
      <Download size={20} className="text-green-600" />
    </Button>
  );
}

import { cn, getAvatarInitials } from "@/lib/utils.js";
// import { formatEntryExitTime } from "@/lib/formatDates.js";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button.jsx";
import { Link } from "@tanstack/react-router";
import { ArrowUpDown, MoreHorizontal, Plus, Minus } from "lucide-react";
import { useTranslation } from "react-i18next";

export const STAFF_LIST_COLUMNS = [
  {
    header: "Logo",
    cell: ({ row }) => (
      <div className="min-h-10 min-w-10 w-10 h-10 rounded-full border-admin-primary border flex justify-center items-center font-medium text-violet-600 bg-violet-50 border-violet-600">
        {getAvatarInitials(row.original.name)}
      </div>
    ),
  },
  {
    accessorKey: "name",
    header: ({ column }) => {
      const { t } = useTranslation();
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t("staff.fields.firstName")}
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: "job_title",
    header: () => {
      const { t } = useTranslation();
      return t("staff.fields.jobTitle");
    },
  },
  {
    accessorKey: "department",
    header: () => {
      const { t } = useTranslation();
      return t("staff.fields.department");
    },
  },
  {
    accessorKey: "on_campus",
    header: () => {
      const { t } = useTranslation();
      return t("staff.fields.status");
    },
    cell: ({ row }) => {
      const label = row.original.on_campus ? "On campus" : "Off campus";
      const variantColor = row.original.on_campus
        ? "bg-green-100 text-green-500"
        : "bg-red-100 text-red-500";
      return (
        <div
          className={cn(
            "w-max text-sm text-center font-medium py-1 px-2 rounded-lg",
            variantColor
          )}
        >
          {label}
        </div>
      );
    },
  },
  // {
  //   accessorKey: "last_entry_time",
  //   header: () => {
  //     const { t } = useTranslation();
  //     return t("entryTime");
  //   },
  //   cell: ({ row }) => {
  //     const date = new Date(row.original.last_entry_time);
  //     const formattedDate = formatEntryExitTime(date);
  //     return (
  //       <div className="flex items-center gap-0.5">
  //         <Plus size={16} className="text-emerald-500" />
  //         {formattedDate}
  //       </div>
  //     );
  //   },
  // },
  // {
  //   accessorKey: "last_exit_time",
  //   header: () => {
  //     const { t } = useTranslation();
  //     return t("exitTime");
  //   },
  //   cell: ({ row }) => {
  //     const date = new Date(row.original.last_exit_time);
  //     const formattedDate = formatEntryExitTime(date);
  //     return (
  //       <div className="flex items-center gap-0.5">
  //         <Minus size={16} className="text-red-500" />
  //         {formattedDate}
  //       </div>
  //     );
  //   },
  // },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const { t } = useTranslation();
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">{t("openMenu")}</span>
              <MoreHorizontal />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>{t("actions")}</DropdownMenuLabel>
            <Link to={`${row.original.id}`}>
              <DropdownMenuItem className="cursor-pointer">
                {t("viewDetail")}
              </DropdownMenuItem>
            </Link>
            <Link href={`${row.original.id}/edit`}>
              <DropdownMenuItem>{t("edit")}</DropdownMenuItem>
            </Link>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];

import { STAFF_LIST_COLUMNS } from "./column";
import { DataTableContainer } from "../data-table-container";
import { useStaff } from "@/api/staff/queries";
import { useFilter } from "@/hooks/use-filter";
import DownloadExcelBtn from "./DownloadExcelBtn";

const StaffsTable = ({ routeId }) => {
  const { searchParams, setSearchParams } = useFilter(routeId);
  const { page, pageSize, search, on_campus, department } = searchParams;

  const skip = (page - 1) * pageSize;
  const { data, isLoading, error } = useStaff({
    skip,
    pageSize,
    search,
    on_campus,
    department,
  });

  if (isLoading) return <div>Loading...</div>;

  if (error || !data) return <pre>{JSON.stringify(error, null, 2)}</pre>;

  const staffData = (data || [])?.map((item) => ({
    id: item.id,
    name: item.person.name + " " + item.person.surname,
    department: item.department,
    on_campus: item.on_campus,
    job_title: item.job_title,
    // last_exit_time: "2025-02-15T08:00:00Z", // TODO: update these fields
    // last_entry_time: "2025-02-15T07:45:00Z",
  }));

  return (
    <DataTableContainer
      columns={STAFF_LIST_COLUMNS}
      data={staffData}
      includeToolbar
      includePagination
      DownloadExcelBtn={DownloadExcelBtn}
      pageIndex={page - 1}
      pageSize={pageSize}
      pageSizeOptions={[10, 20, 50]}
      onPaginationChange={({ pageIndex, pageSize }) => {
        setSearchParams({ page: pageIndex + 1, pageSize });
      }}
      searchParams={searchParams}
      setSearchParams={setSearchParams}
      searchPlaceholder="Search Staff..."
      newLink="/dashboard/staffs/new"
      newButtonLabel="+ Staff"
      type="staff"
      filterColumnKey="name"
    />
  );
};

export default StaffsTable;

// ! TODO: need Zod schema in the future
import { cn, getAvatarInitials } from "@/lib/utils.js";
import { formatEntryExitTime } from "@/lib/formatDates.js";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button.jsx";
import { Link } from "@tanstack/react-router";
import { ArrowUpDown, MoreHorizontal } from "lucide-react";
import { useTranslation } from "react-i18next";

export const STUDENT_LIST_COLUMNS = [
  {
    header: "Logo",
    cell: ({ row }) => (
      <div className="min-h-10 min-w-10 w-10 h-10 rounded-full border-admin-primary border flex justify-center items-center font-medium text-violet-600 bg-violet-50 border-violet-600">
        {getAvatarInitials(row.original.name)}
      </div>
    ),
  },
  {
    accessorKey: "name",
    header: ({ column }) => {
      const { t } = useTranslation();
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t("name")}
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: "major",
    header: () => {
      const { t } = useTranslation();
      return t("major");
    },
  },
  {
    accessorKey: "graduation_year",
    header: () => {
      const { t } = useTranslation();
      return t("gradYear");
    },
    cell: ({ row }) => {
      return (
        <div className="w-full text-center">{row.original.graduation_year}</div>
      );
    },
  },
  {
    accessorKey: "on_campus",
    header: () => {
      const { t } = useTranslation();
      return t("status");
    },
    cell: ({ row }) => {
      const { t } = useTranslation();
      const label = row.original.on_campus ? t("onCampus") : t("offCampus");
      const variantColor = row.original.on_campus
        ? "bg-green-100 text-green-500"
        : "bg-red-100 text-red-500";
      return (
        <div
          className={cn(
            "w-max text-sm text-center font-medium py-1 px-2 rounded-lg",
            variantColor
          )}
        >
          {label}
        </div>
      );
    },
  },
  // {
  //   accessorKey: "datetime",
  //   header: () => {
  //     const { t } = useTranslation();
  //     return t('dateTime');
  //   },
  //   cell: ({ row }) => {
  //     const date = new Date(row.original.datetime);
  //     const formattedDate = formatEntryExitTime(date);
  //     return <span>{formattedDate}</span>;
  //   },
  // },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const { t } = useTranslation();
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">{t("openMenu")}</span>
              <MoreHorizontal />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>{t("actions")}</DropdownMenuLabel>
            <Link to={`${row.original.id}`}>
              <DropdownMenuItem className="cursor-pointer">
                {t("viewDetail")}
              </DropdownMenuItem>
            </Link>
            <Link href={`${row.original.id}/edit`}>
              <DropdownMenuItem>{t("edit")}</DropdownMenuItem>
            </Link>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];

// export const STUDENT_RECORDS_COLUMNS = [
//   {
//     header: "Logo",
//     cell: ({ row }) => (
//       <div className="min-h-10 min-w-10 w-10 h-10 rounded-full border-admin-primary border flex justify-center items-center font-medium text-violet-600 bg-violet-50 border-violet-600">
//         {getAvatarInitials(row.original.name)}
//       </div>
//     ),
//   },
//   {
//     accessorKey: "name",
//     header: ({ column }) => {
//       const { t } = useTranslation();
//       return (
//         <Button
//           variant="ghost"
//           onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
//         >
//           {t("name")}
//           <ArrowUpDown className="ml-2 h-4 w-4" />
//         </Button>
//       );
//     },
//   },
//   {
//     accessorKey: "major",
//     header: () => {
//       const { t } = useTranslation();
//       return t("major");
//     },
//   },
//   {
//     accessorKey: "graduation_year",
//     header: () => {
//       const { t } = useTranslation();
//       return t("gradYear");
//     },
//     cell: ({ row }) => {
//       return (
//         <div className="w-full text-center">{row.original.graduation_year}</div>
//       );
//     },
//   },
//   {
//     accessorKey: "datetime",
//     header: () => {
//       const { t } = useTranslation();
//       return t("dateTime");
//     },
//     cell: ({ row }) => {
//       const date = new Date(row.original.datetime);
//       const formattedDate = formatEntryExitTime(date);
//       return <span>{formattedDate}</span>;
//     },
//   },
//   {
//     accessorKey: "action",
//     header: "Action",
//     cell: ({ row }) => {
//       const isEntered = row.original.action === "in";
//       const label = isEntered ? "Entered" : "Exited";
//       const variantColor = isEntered ? "text-green-500" : "text-red-500";
//       return (
//         <div
//           className={cn(
//             "w-full text-sm text-center font-medium py-0.5 px-2 rounded-lg",
//             variantColor
//           )}
//         >
//           {label}
//         </div>
//       );
//     },
//   },

//   {
//     accessorKey: "made_by",
//     header: () => {
//       const { t } = useTranslation();
//       return t("madeBy");
//     },
//   },
//   {
//     id: "actions",
//     enableHiding: false,
//     cell: ({ row }) => {
//       const { t } = useTranslation();
//       return (
//         <DropdownMenu>
//           <DropdownMenuTrigger asChild>
//             <Button variant="ghost" className="h-8 w-8 p-0">
//               <span className="sr-only">{t("openMenu")}</span>
//               <MoreHorizontal />
//             </Button>
//           </DropdownMenuTrigger>
//           <DropdownMenuContent align="end">
//             <DropdownMenuLabel>{t("actions")}</DropdownMenuLabel>
//             <Link to={`${row.original.id}`}>
//               <DropdownMenuItem className="cursor-pointer">
//                 {t("viewDetail")}
//               </DropdownMenuItem>
//             </Link>
//             <Link href={`${row.original.id}/edit`}>
//               <DropdownMenuItem>{t("edit")}</DropdownMenuItem>
//             </Link>
//           </DropdownMenuContent>
//         </DropdownMenu>
//       );
//     },
//   },
// ];

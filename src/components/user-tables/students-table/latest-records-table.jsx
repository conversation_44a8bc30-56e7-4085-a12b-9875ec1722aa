// import { STUDENT_RECORDS_COLUMNS } from "./columns";
// import { DataTableContainer } from "../data-table-container";
// import { mockStudentRecordsData } from "@/lib/store";

// const LatestRecordsTable = ({ fullVersion = true }) => {
//   const columns = fullVersion
//     ? STUDENT_RECORDS_COLUMNS
//     : STUDENT_RECORDS_COLUMNS.filter((col) => col.id !== "actions");

//   return (
//     <DataTableContainer
//       columns={columns}
//       data={mockStudentRecordsData}
//       includeToolbar={false}
//     />
//   );
// };

// export default LatestRecordsTable;

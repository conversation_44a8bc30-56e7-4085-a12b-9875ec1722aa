// import { STUDENT_RECORDS_COLUMNS } from "./columns";
// import { DataTableContainer } from "../data-table-container";
// import { mockStudentRecordsData } from "@/lib/store";
// import { useStudentRecords } from "@/api/students/queries";

// const StudentsRecordsTable = () => {
//   const { data, isLoading, error } = useStudentRecords();

//   if (isLoading) return <div>Loading...</div>;

//   if (error || !data) return <pre>{JSON.stringify(error, null, 2)}</pre>;

//   return (
//     <DataTableContainer
//       columns={STUDENT_RECORDS_COLUMNS}
//       data={mockStudentRecordsData}
//       includeToolbar={false}
//       includePagination={false}
//     />
//   );
// };

// export default StudentsRecordsTable;

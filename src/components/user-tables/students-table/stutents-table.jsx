import { useFilter } from "@/hooks/use-filter";
import { DataTableContainer } from "../data-table-container";
import { STUDENT_LIST_COLUMNS } from "./columns";
import { useStudents } from "@/api/students/queries";

const StudentsTable = ({ routeId }) => {
  const { searchParams, setSearchParams } = useFilter(routeId);
  const { page, pageSize, search, on_campus, department } = searchParams;

  const skip = (page - 1) * pageSize;
  const {
    data: students,
    isLoading,
    error,
  } = useStudents({
    skip,
    pageSize,
    search,
    on_campus,
    department,
  });

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (error || !students) {
    return <div>No data</div>;
  }

  const studentsList = students?.map((student) => {
    return {
      id: student.id,
      name: student.person.name + " " + student.person.surname,
      major: student.department,
      graduation_year: student.graduation_year,
      on_campus: student.on_campus || false,
      datetime: "2025-02-15T08:00:00Z",
    };
  });

  return (
    <DataTableContainer
      columns={STUDENT_LIST_COLUMNS}
      data={studentsList || []}
      includeToolbar={true}
      includePagination={true}
      pageIndex={page - 1}
      pageSize={pageSize}
      pageSizeOptions={[10, 20, 50]}
      onPaginationChange={({ pageIndex, pageSize }) => {
        setSearchParams({ page: pageIndex + 1, pageSize });
      }}
      searchParams={searchParams}
      setSearchParams={setSearchParams}
      searchPlaceholder="Search Students..."
      newLink="/dashboard/students/new"
      newButtonLabel="+ Student"
      type="student"
      filterColumnKey="name"
    />
  );
};

export default StudentsTable;

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import {useTranslation} from "react-i18next";

// Mock data for AWS images
const MOCK_IMAGES = [
  {
    id: 1,
    image:
      "https://images.unsplash.com/photo-1552058544-f2b08422138a?q=80&w=1899&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    person: { name: "<PERSON>", surname: "<PERSON><PERSON>" },
  },
  {
    id: 2,
    image:
      "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    person: { name: "<PERSON>", surname: "<PERSON>" },
  },
  {
    id: 3,
    image:
      "https://images.unsplash.com/photo-1544723795-3fb6469f5b39?q=80&w=1889&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    person: { name: "Alice", surname: "Johnson" },
  },
];

const TrainingImages = () => {
  const { t } = useTranslation();

  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(false);

  // Simulate fetching images on mount
  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      setImages(MOCK_IMAGES);
      setLoading(false);
    }, 500);
  }, []);

  const handleTrain = () => {
    // Simulate training action (you can add more logic here)
    console.log("Training data...");
    setLoading(true);
    setTimeout(() => {
      // For example, reset to initial mock data after training
      setImages(MOCK_IMAGES);
      setLoading(false);
    }, 500);
  };

  const handleDelete = (id) => {
    // Remove image from state based on id
    setImages((prev) => prev.filter((img) => img.id !== id));
  };

  return (
    <div className="container mx-auto py-10 px-4">
      {/* Train Data Button */}
      <div className="mb-4">
        <Button variant="outline" onClick={handleTrain}>
          {t('trainData')}
        </Button>
      </div>

      {/* Images Grid */}
      {loading ? (
        <div className="flex justify-center">
          <p>{t('loading')}...</p>
        </div>
      ) : (
        <div className="grid grid-cols-4 gap-4">
          {images.map((item) => (
            <div
              key={item.id}
              className="relative rounded overflow-hidden shadow"
            >
              <img
                src={item.image}
                alt={`Image ${item.id}`}
                className="w-full h-96 object-cover"
              />
              {/* Overlay with person's name and delete button */}
              <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent flex flex-col justify-between p-2">
                <div className="text-white font-medium">
                  {item.person.name} {item.person.surname}
                </div>
                <button
                  onClick={() => handleDelete(item.id)}
                  className="self-start bg-red-500 hover:bg-red-600 text-white p-1 rounded-full hover:scale-110 transition-transform"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default TrainingImages;

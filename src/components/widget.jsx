import { UserRound } from "lucide-react"; // we can import icons from lucide-react library
import { cn } from "@/lib/utils";
import { useTranslation } from "react-i18next";
import { Link } from "@tanstack/react-router";

const Widget = ({ num, all, title, label, navigationLink = "" }) => {
  const { t } = useTranslation();

  const percent = Math.floor((num / all) * 100);

  let color; // var is not recommended to use
  if (label === "on_campus") {
    color = "green";
  } else if (label === "not_on_campus") {
    color = "red";
  } else {
    color = "purple";
  }

  // - Bad Practice: `text-${color}-500` -> not always works
  // + Good Practice: define complete Tailwind class names
  // Check the link form more info: https://tailwindcss.com/docs/detecting-classes-in-source-files
  const textColorClasses = {
    red: "text-red-800",
    purple: "text-purple-800",
    green: "text-green-800",
  };

  const bgColorClasses = {
    red: "bg-red-600",
    purple: "bg-purple-600",
    green: "bg-green-600",
  };

  return (
    <div className="w-72 p-4 rounded-lg shadow-md bg-white">
      <div className="flex justify-between h-full">
        <div className="flex flex-col justify-between gap-2">
          <h3 className="text-xl font-medium text-gray-600">{title}</h3>
          <p className="text-3xl font-bold text-gray-600">{num}</p>
          <Link
            to={navigationLink}
            className="underline text-sm underline-offset-4 text-gray-500"
          >
            {t("viewList")}
          </Link>
        </div>

        <div className="flex flex-col justify-between h-24 items-center">
          {/* Use the utility function to merge static class names */}
          <div className={cn("text-xl font-medium", textColorClasses[color])}>
            {percent}%
          </div>
          <div
            className={cn(
              "bg-opacity-70 rounded-md w-8 h-8 flex items-center justify-center",
              bgColorClasses[color]
            )}
          >
            <UserRound className="w-5 h-5" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Widget;

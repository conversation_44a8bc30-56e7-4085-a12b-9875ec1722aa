import { useState, useEffect } from "react";

/**
 * @param {any} value - value to debounce
 * @param {number} delay - debounce delay in ms
 * @returns debounced value
 */
export function useDebounce(value, delay = 300) {
  const [debounced, setDebounced] = useState(value);
  useEffect(() => {
    const handler = setTimeout(() => setDebounced(value), delay);
    return () => clearTimeout(handler);
  }, [value, delay]);
  return debounced;
}

import { getRouteApi } from "@tanstack/react-router";
import { clearEmptyParams } from "../lib/utils";

export const useFilter = (routeId) => {
  const routeApi = getRouteApi(routeId);
  const navigate = routeApi.useNavigate();
  const searchParams = routeApi.useSearch();

  const setSearchParams = (partialFiters) => {
    navigate({
      search: clearEmptyParams({ ...searchParams, ...partialFiters }),
      resetScroll: false,
    });
  };
  const resetSearchParams = () => navigate({ search: {} });
  return { searchParams, setSearchParams, resetSearchParams };
};

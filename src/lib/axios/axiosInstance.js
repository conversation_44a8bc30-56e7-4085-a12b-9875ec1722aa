import { authStore, getToken } from "@/store/auth/auth-store";
import { router } from "@/main";
import axios from "axios";

const BASE_URL = `${import.meta.env.VITE_BASE_URL}`;

const apiInstance = axios.create({
  baseURL: BASE_URL,
  // withCredentials: true,
  headers: {
    "Content-Type": "application/json",
  },
});

apiInstance.interceptors.request.use(
  (config) => {
    const token = getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

apiInstance.interceptors.response.use(
  (response) => response,
  async (error) => {
    // const originalRequest = error.config;
    console.log("EEEE", error);
    console.log(error.status, error.status === 401);
    if (error.status === 401) {
      authStore.getState().logout();
      console.log("Navigate---1");
      router.navigate({ to: "/login" });
      console.log("Navigate---2");
      // originalRequest._retry = true;
      // try {
      //   const refreshToken = useAuthStore.getState().refreshToken;
      //   if (!refreshToken) throw new Error("No refresh token available");
      //   const { data } = await axios.post(`${BASE_URL}/user/refresh-token/`, {
      //     refresh_token: refreshToken,
      //   });
      //   useAuthStore.getState().signIn(data); // Update store with new tokens
      //   originalRequest.headers.Authorization = `Bearer ${data.access_token}`;
      //   return apiInstance(originalRequest);
      // } catch (refreshError) {
      //   useAuthStore.getState().logout(); // Logout user if refresh fails
      //   return Promise.reject(refreshError);
      // }
    }
    return Promise.reject(error);
  }
);

export { apiInstance, BASE_URL };

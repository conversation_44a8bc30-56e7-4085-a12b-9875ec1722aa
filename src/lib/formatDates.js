/**
 * Formats a date into "HH:mm - DD Mon" (24-hour format, short month).
 *
 * @param {string|Date} dateInput - The date to format.
 * @returns {string} - A string like "08:30 - 24 Dec".
 */
export function formatEntryExitTime(dateInput) {
    const date = new Date(dateInput);

    if (isNaN(date.getTime())) {
        console.error("Invalid date input:", dateInput);
        return "Invalid date";
    }

    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    const day = String(date.getDate()).padStart(2, '0');
    const shortMonths = ["Jan", "Feb", "Mar", "Apr", "May", "Jun",
        "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    const month = shortMonths[date.getMonth()];

    return `${hours}:${minutes}, ${day} ${month}`;
}
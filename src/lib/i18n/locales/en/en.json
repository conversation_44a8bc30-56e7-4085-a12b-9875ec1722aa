{"appSidebar": {"main": {"title": "Main", "view": "View", "camera": "Camera"}, "lists": {"title": "Lists", "students": "Students", "staff": "Staff"}, "records": {"title": "Records", "studentRecords": "Student Records", "staffRecords": "Staff Records"}, "data": {"title": "Data", "statistics": "Statistics", "images": "Images"}, "user": {"title": "User", "profile": "Profile", "logout": "Logout"}}, "staff": {"createFormTitle": "Create staff", "updateFormTitle": "Update staff", "saveChanges": "Save changes", "fields": {"firstName": "Name", "lastName": "Last Name", "middleName": "Middle name", "email": "Email", "jobTitle": "Job Title", "department": "Department", "hireDate": "Hire Date", "dateOfBirth": "Date of Birth", "address": "Address", "gender": "Gender", "status": "Status", "phoneNumber": "Phone Number", "nationality": "Nationality", "male": "Male", "female": "Female", "submit": "Submit", "placeholders": {"jobTitle": "Enter job title", "address": "Enter address", "selectDate": "Select date", "department": "Enter department", "firstName": "Enter first name", "lastName": "Enter last name", "middleName": "Enter middle name", "nationality": "Enter nationality"}}}, "button": {"deleting": "Deleting", "delete": "Delete"}, "records": {"entered": "Entered", "exited": "Exited"}, "studentsData": "Students Data", "stuffData": "Staff Data", "viewList": "View List", "onCampus": "On campus", "offCampus": "Off campus", "total": "Total", "latestRecords": "Latest Records", "phone": "Phone", "gender": "Gender", "major": "Major", "gradYear": "Graduation Year", "onCampStat": "On-Campus Status", "lastActive": "Last Active", "edit": "Edit", "delete": "Delete", "matchingImages": "Matching Images", "uploadNew": "Upload New", "loading": "Loading", "noImageSelected": "No image selected", "changePassword": "Change the password", "currentPassword": "Current Password", "currentPasswordRequired": "Current password is required", "newPassword": "New Password", "newPasswordRequired": "New password is required", "confirmPassword": "Confirm New Password", "notMatch": "Passwords do not match", "change": "Change", "changeUsername": "Change the username", "newUsername": "New Username", "usernameRequired": "Username is required", "passwordRequired": "Password is required", "totalEntries": "Total Entries/Exits", "peakHours": "Peak Hours", "trainData": "Train Data", "name": "Name", "openMenu": "Open menu", "actions": "Actions", "viewDetail": "View Detail", "status": "Status", "dateTime": "Date / Time", "chooseCamera": "<PERSON>ose <PERSON>", "camIsOff": "Camera is off", "notCaptured": "No image captured yet", "identifying": "Identifying", "takePhoto": "Take Photo", "enter": "Enter", "exit": "Exit", "turnCameraOn": "Turn Camera On", "turnCameraOff": "Turn Camera Off", "cameras": "Cameras", "studExited": "Student Exited!", "studEntered": "Student Entered!", "recordsStudents": "Records Students", "stuffsList": "Staffs List", "studList": "Students List", "studRegistration": "Registration of student", "firstName": "First name", "lastName": "Last name", "middleName": "Middle name", "phoneNumber": "Phone Number", "dateOfBirth": "Date of Birth", "male": "Male", "female": "Female", "nationality": "Nationality", "register": "Register", "department": "Department", "placeholders": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "middleName": "Enter middle name", "nationality": "Enter nationality", "graduationYear": "Enter graduation year", "department": "Enter department", "selectDate": "Select a date"}, "entryTime": "Entry Time", "exitTime": "Exit Time", "language": "Language", "english": " English", "russian": " Russian"}
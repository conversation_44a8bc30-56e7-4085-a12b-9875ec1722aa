export const mockStudentData = [
  {
    id: 1,
    name: "<PERSON>",
    major: "Computer Science",
    graduation_year: 2024,
    on_campus: true,
    datetime: "2025-02-15T08:00:00Z",
  },
  {
    id: 2,
    name: "<PERSON>",
    major: "Communication & Media",
    graduation_year: 2023,
    on_campus: false,
    datetime: "2025-02-15T08:15:00Z",
  },
  {
    id: 3,
    name: "<PERSON>",
    major: "Economics",
    graduation_year: 2025,
    on_campus: true,
    datetime: "2025-02-15T08:30:00Z",
  },
  {
    id: 4,
    name: "<PERSON>",
    major: "Environmental Sciences",
    graduation_year: 2022,
    on_campus: false,
    datetime: "2025-02-15T08:45:00Z",
  },
  {
    id: 5,
    name: "<PERSON>",
    major: "Computer Science",
    graduation_year: 2024,
    on_campus: true,
    datetime: "2025-02-15T09:00:00Z",
  },
  {
    id: 6,
    name: "<PERSON>",
    major: "Communication & Media",
    graduation_year: 2023,
    on_campus: false,
    datetime: "2025-02-15T09:15:00Z",
  },
  {
    id: 7,
    name: "<PERSON>",
    major: "Economics",
    graduation_year: 2025,
    on_campus: true,
    datetime: "2025-02-15T09:30:00<PERSON>",
  },
  {
    id: 8,
    name: "<PERSON>",
    major: "Environmental Sciences",
    graduation_year: 2022,
    on_campus: false,
    datetime: "2025-02-15T09:45:00Z",
  },
  {
    id: 9,
    name: "Isabella Nguyen",
    major: "Computer Science",
    graduation_year: 2024,
    on_campus: true,
    datetime: "2025-02-15T10:00:00Z",
  },
  {
    id: 10,
    name: "Jack <PERSON>",
    major: "Communication & Media",
    graduation_year: 2023,
    on_campus: false,
    datetime: "2025-02-15T10:15:00Z",
  },
];

export const mockStaffData = [
  {
    id: 1,
    name: "John Doe",
    department: "Maintenance",
    phone: "************",
    on_campus: true,
    last_exit_time: "2025-02-15T08:00:00Z",
    last_entry_time: "2025-02-15T07:45:00Z",
  },
  {
    id: 2,
    name: "Mary Smith",
    department: "Kitchen",
    phone: "************",
    on_campus: false,
    last_exit_time: "2025-02-15T08:15:00Z",
    last_entry_time: "2025-02-15T08:05:00Z",
  },
  {
    id: 3,
    name: "Alice Johnson",
    department: "Security",
    phone: "************",
    on_campus: true,
    last_exit_time: "2025-02-15T08:30:00Z",
    last_entry_time: "2025-02-15T08:20:00Z",
  },
  {
    id: 4,
    name: "Robert Brown",
    department: "Janitorial",
    phone: "************",
    on_campus: false,
    last_exit_time: "2025-02-15T08:45:00Z",
    last_entry_time: "2025-02-15T08:35:00Z",
  },
  {
    id: 5,
    name: "James Wilson",
    department: "Maintenance",
    phone: "************",
    on_campus: true,
    last_exit_time: "2025-02-15T09:00:00Z",
    last_entry_time: "2025-02-15T08:50:00Z",
  },
  {
    id: 6,
    name: "Patricia Garcia",
    department: "Kitchen",
    phone: "************",
    on_campus: true,
    last_exit_time: "2025-02-15T09:15:00Z",
    last_entry_time: "2025-02-15T09:05:00Z",
  },
  {
    id: 7,
    name: "Michael Martinez",
    department: "Security",
    phone: "************",
    on_campus: false,
    last_exit_time: "2025-02-15T09:30:00Z",
    last_entry_time: "2025-02-15T09:20:00Z",
  },
  {
    id: 8,
    name: "Jennifer Davis",
    department: "Janitorial",
    phone: "************",
    on_campus: true,
    last_exit_time: "2025-02-15T09:45:00Z",
    last_entry_time: "2025-02-15T09:35:00Z",
  },
  {
    id: 9,
    name: "William Miller",
    department: "Maintenance",
    phone: "************",
    on_campus: false,
    last_exit_time: "2025-02-15T10:00:00Z",
    last_entry_time: "2025-02-15T09:50:00Z",
  },
  {
    id: 10,
    name: "Elizabeth Anderson",
    department: "Kitchen",
    phone: "************",
    on_campus: true,
    last_exit_time: "2025-02-15T10:15:00Z",
    last_entry_time: "2025-02-15T10:05:00Z",
  },
];

export const mockStudentRecordsData = [
  {
    id: 1,
    name: "Alice Johnson",
    major: "Computer Science",
    graduation_year: 2024,
    action: "in", // or "out"
    datetime: "2025-02-15T08:00:00Z",
    made_by: "Security Name",
  },
  {
    id: 2,
    name: "Bob Smith",
    major: "Communication & Media",
    graduation_year: 2023,
    action: "out",
    datetime: "2025-02-15T08:15:00Z",
    made_by: "Security Name",
  },
  {
    id: 3,
    name: "Catherine Lee",
    major: "Economics",
    graduation_year: 2025,
    action: "in",
    datetime: "2025-02-15T08:30:00Z",
    made_by: "Security Name",
  },
  {
    id: 4,
    name: "David Kim",
    major: "Environmental Sciences",
    graduation_year: 2022,
    action: "out",
    datetime: "2025-02-15T08:45:00Z",
    made_by: "Security Name",
  },
  {
    id: 5,
    name: "Eva Garcia",
    major: "Computer Science",
    graduation_year: 2024,
    action: "in",
    datetime: "2025-02-15T09:00:00Z",
    made_by: "Security Name",
  },
  {
    id: 6,
    name: "Frank Miller",
    major: "Communication & Media",
    graduation_year: 2023,
    action: "out",
    datetime: "2025-02-15T09:15:00Z",
    made_by: "Security Name",
  },
  {
    id: 7,
    name: "Grace Patel",
    major: "Economics",
    graduation_year: 2025,
    action: "in",
    datetime: "2025-02-15T09:30:00Z",
    made_by: "Security Name",
  },
  {
    id: 8,
    name: "Henry Wu",
    major: "Environmental Sciences",
    graduation_year: 2022,
    action: "out",
    datetime: "2025-02-15T09:45:00Z",
    made_by: "Security Name",
  },
  {
    id: 9,
    name: "Isabella Nguyen",
    major: "Computer Science",
    graduation_year: 2024,
    action: "in",
    datetime: "2025-02-15T10:00:00Z",
    made_by: "Security Name",
  },
  {
    id: 10,
    name: "Jack Wilson",
    major: "Communication & Media",
    graduation_year: 2023,
    action: "out",
    datetime: "2025-02-15T10:15:00Z",
    made_by: "Security Name",
  },
];

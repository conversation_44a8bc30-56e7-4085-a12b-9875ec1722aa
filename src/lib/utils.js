import { clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs) {
  return twMerge(clsx(inputs));
}

export function getAvatarInitials(name) {
  if (!name) return "";

  const words = name.trim().split(" ");

  if (words.length === 1) {
    return words[0].slice(0, 2).toUpperCase();
  }

  return `${words[0][0]}${words[1][0]}`.toUpperCase();
}

export function truncateText(text, limit) {
  if (text.length > limit) {
    return text.substring(0, limit) + "...";
  }
  return text;
}

export function formatDate(dateInput, locale = "ru-RU", includeTime = false) {
  const date = typeof dateInput === "string" ? new Date(dateInput) : dateInput;

  const options = {
    year: "numeric",
    month: "long",
    day: "numeric",
  };

  if (includeTime) {
    options.hour = "2-digit";
    options.minute = "2-digit";

    options.hour12 = false;
  }

  const formatter = new Intl.DateTimeFormat(locale, options);
  return formatter.format(date);
}

export const clearEmptyParams = (search) => {
  const newSearch = { ...search };
  Object.keys(newSearch).forEach((key) => {
    const value = newSearch[key];
    if (
      value === "" ||
      value === undefined ||
      (typeof value === "number" && isNaN(value))
    ) {
      delete newSearch[key];
    }
  });
  return newSearch;
};

export const getProfileImage = (imageName) => {
  if (!imageName) return null;
  const IMAGE_URL = `${import.meta.env.VITE_IMAGE_URL}`;
  return `${IMAGE_URL}/files/${imageName}`;
};

export const getStudentProfileImageById = (id) => {
  if (!id) return null;
  const IMAGE_URL = `${import.meta.env.VITE_BASE_URL}`;
  console.log(
    "IMAGE_URL",
    `${IMAGE_URL}/srv/security/demographic/persons/${id}/profile_picture/`
  );
  return `${IMAGE_URL}/srv/security/demographic/persons/${id}/profile_picture/`;
};

import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { RouterProvider, createRouter } from "@tanstack/react-router";

import { routeTree } from "./routeTree.gen";
import "./index.css";
import "./lib/i18n/i18n.js";
import QueryProvider from "./lib/query-client/QueryProvider";
import { Toaster } from "sonner";

// Create a new router instance
export const router = createRouter({ routeTree });

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <QueryProvider>
      <RouterProvider router={router}/>
    </QueryProvider>
    <Toaster richColors />
  </StrictMode>
);

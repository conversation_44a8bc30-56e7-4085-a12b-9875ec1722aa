/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createFileRoute } from '@tanstack/react-router'

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as LoginImport } from './routes/login'
import { Route as DashboardImport } from './routes/dashboard'
import { Route as IndexImport } from './routes/index'
import { Route as DashboardIndexImport } from './routes/dashboard/index'
import { Route as DashboardTrainingImagesImport } from './routes/dashboard/training-images'
import { Route as DashboardStudentRecordsImport } from './routes/dashboard/student-records'
import { Route as DashboardStatisticsImport } from './routes/dashboard/statistics'
import { Route as DashboardStaffRecordsImport } from './routes/dashboard/staff-records'
import { Route as DashboardProfileImport } from './routes/dashboard/profile'
import { Route as DashboardCameraImport } from './routes/dashboard/camera'
import { Route as DashboardStudentsIndexImport } from './routes/dashboard/students/index'
import { Route as DashboardStaffsIndexImport } from './routes/dashboard/staffs/index'
import { Route as DashboardStudentsStudentIdImport } from './routes/dashboard/students/$studentId'
import { Route as DashboardStaffsStaffIdImport } from './routes/dashboard/staffs/$staffId'
import { Route as DashboardStaffsStaffIdEditImport } from './routes/dashboard/staffs_/$staffId/edit'

// Create Virtual Routes

const DashboardStudentsNewLazyImport = createFileRoute(
  '/dashboard/students/new',
)()
const DashboardStaffsNewLazyImport = createFileRoute('/dashboard/staffs/new')()

// Create/Update Routes

const LoginRoute = LoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const DashboardRoute = DashboardImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const DashboardIndexRoute = DashboardIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => DashboardRoute,
} as any)

const DashboardTrainingImagesRoute = DashboardTrainingImagesImport.update({
  id: '/training-images',
  path: '/training-images',
  getParentRoute: () => DashboardRoute,
} as any)

const DashboardStudentRecordsRoute = DashboardStudentRecordsImport.update({
  id: '/student-records',
  path: '/student-records',
  getParentRoute: () => DashboardRoute,
} as any)

const DashboardStatisticsRoute = DashboardStatisticsImport.update({
  id: '/statistics',
  path: '/statistics',
  getParentRoute: () => DashboardRoute,
} as any)

const DashboardStaffRecordsRoute = DashboardStaffRecordsImport.update({
  id: '/staff-records',
  path: '/staff-records',
  getParentRoute: () => DashboardRoute,
} as any)

const DashboardProfileRoute = DashboardProfileImport.update({
  id: '/profile',
  path: '/profile',
  getParentRoute: () => DashboardRoute,
} as any)

const DashboardCameraRoute = DashboardCameraImport.update({
  id: '/camera',
  path: '/camera',
  getParentRoute: () => DashboardRoute,
} as any)

const DashboardStudentsIndexRoute = DashboardStudentsIndexImport.update({
  id: '/students/',
  path: '/students/',
  getParentRoute: () => DashboardRoute,
} as any)

const DashboardStaffsIndexRoute = DashboardStaffsIndexImport.update({
  id: '/staffs/',
  path: '/staffs/',
  getParentRoute: () => DashboardRoute,
} as any)

const DashboardStudentsNewLazyRoute = DashboardStudentsNewLazyImport.update({
  id: '/students/new',
  path: '/students/new',
  getParentRoute: () => DashboardRoute,
} as any).lazy(() =>
  import('./routes/dashboard/students/new.lazy').then((d) => d.Route),
)

const DashboardStaffsNewLazyRoute = DashboardStaffsNewLazyImport.update({
  id: '/staffs/new',
  path: '/staffs/new',
  getParentRoute: () => DashboardRoute,
} as any).lazy(() =>
  import('./routes/dashboard/staffs/new.lazy').then((d) => d.Route),
)

const DashboardStudentsStudentIdRoute = DashboardStudentsStudentIdImport.update(
  {
    id: '/students/$studentId',
    path: '/students/$studentId',
    getParentRoute: () => DashboardRoute,
  } as any,
)

const DashboardStaffsStaffIdRoute = DashboardStaffsStaffIdImport.update({
  id: '/staffs/$staffId',
  path: '/staffs/$staffId',
  getParentRoute: () => DashboardRoute,
} as any)

const DashboardStaffsStaffIdEditRoute = DashboardStaffsStaffIdEditImport.update(
  {
    id: '/staffs_/$staffId/edit',
    path: '/staffs/$staffId/edit',
    getParentRoute: () => DashboardRoute,
  } as any,
)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/dashboard': {
      id: '/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardImport
      parentRoute: typeof rootRoute
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginImport
      parentRoute: typeof rootRoute
    }
    '/dashboard/camera': {
      id: '/dashboard/camera'
      path: '/camera'
      fullPath: '/dashboard/camera'
      preLoaderRoute: typeof DashboardCameraImport
      parentRoute: typeof DashboardImport
    }
    '/dashboard/profile': {
      id: '/dashboard/profile'
      path: '/profile'
      fullPath: '/dashboard/profile'
      preLoaderRoute: typeof DashboardProfileImport
      parentRoute: typeof DashboardImport
    }
    '/dashboard/staff-records': {
      id: '/dashboard/staff-records'
      path: '/staff-records'
      fullPath: '/dashboard/staff-records'
      preLoaderRoute: typeof DashboardStaffRecordsImport
      parentRoute: typeof DashboardImport
    }
    '/dashboard/statistics': {
      id: '/dashboard/statistics'
      path: '/statistics'
      fullPath: '/dashboard/statistics'
      preLoaderRoute: typeof DashboardStatisticsImport
      parentRoute: typeof DashboardImport
    }
    '/dashboard/student-records': {
      id: '/dashboard/student-records'
      path: '/student-records'
      fullPath: '/dashboard/student-records'
      preLoaderRoute: typeof DashboardStudentRecordsImport
      parentRoute: typeof DashboardImport
    }
    '/dashboard/training-images': {
      id: '/dashboard/training-images'
      path: '/training-images'
      fullPath: '/dashboard/training-images'
      preLoaderRoute: typeof DashboardTrainingImagesImport
      parentRoute: typeof DashboardImport
    }
    '/dashboard/': {
      id: '/dashboard/'
      path: '/'
      fullPath: '/dashboard/'
      preLoaderRoute: typeof DashboardIndexImport
      parentRoute: typeof DashboardImport
    }
    '/dashboard/staffs/$staffId': {
      id: '/dashboard/staffs/$staffId'
      path: '/staffs/$staffId'
      fullPath: '/dashboard/staffs/$staffId'
      preLoaderRoute: typeof DashboardStaffsStaffIdImport
      parentRoute: typeof DashboardImport
    }
    '/dashboard/students/$studentId': {
      id: '/dashboard/students/$studentId'
      path: '/students/$studentId'
      fullPath: '/dashboard/students/$studentId'
      preLoaderRoute: typeof DashboardStudentsStudentIdImport
      parentRoute: typeof DashboardImport
    }
    '/dashboard/staffs/new': {
      id: '/dashboard/staffs/new'
      path: '/staffs/new'
      fullPath: '/dashboard/staffs/new'
      preLoaderRoute: typeof DashboardStaffsNewLazyImport
      parentRoute: typeof DashboardImport
    }
    '/dashboard/students/new': {
      id: '/dashboard/students/new'
      path: '/students/new'
      fullPath: '/dashboard/students/new'
      preLoaderRoute: typeof DashboardStudentsNewLazyImport
      parentRoute: typeof DashboardImport
    }
    '/dashboard/staffs/': {
      id: '/dashboard/staffs/'
      path: '/staffs'
      fullPath: '/dashboard/staffs'
      preLoaderRoute: typeof DashboardStaffsIndexImport
      parentRoute: typeof DashboardImport
    }
    '/dashboard/students/': {
      id: '/dashboard/students/'
      path: '/students'
      fullPath: '/dashboard/students'
      preLoaderRoute: typeof DashboardStudentsIndexImport
      parentRoute: typeof DashboardImport
    }
    '/dashboard/staffs_/$staffId/edit': {
      id: '/dashboard/staffs_/$staffId/edit'
      path: '/staffs/$staffId/edit'
      fullPath: '/dashboard/staffs/$staffId/edit'
      preLoaderRoute: typeof DashboardStaffsStaffIdEditImport
      parentRoute: typeof DashboardImport
    }
  }
}

// Create and export the route tree

interface DashboardRouteChildren {
  DashboardCameraRoute: typeof DashboardCameraRoute
  DashboardProfileRoute: typeof DashboardProfileRoute
  DashboardStaffRecordsRoute: typeof DashboardStaffRecordsRoute
  DashboardStatisticsRoute: typeof DashboardStatisticsRoute
  DashboardStudentRecordsRoute: typeof DashboardStudentRecordsRoute
  DashboardTrainingImagesRoute: typeof DashboardTrainingImagesRoute
  DashboardIndexRoute: typeof DashboardIndexRoute
  DashboardStaffsStaffIdRoute: typeof DashboardStaffsStaffIdRoute
  DashboardStudentsStudentIdRoute: typeof DashboardStudentsStudentIdRoute
  DashboardStaffsNewLazyRoute: typeof DashboardStaffsNewLazyRoute
  DashboardStudentsNewLazyRoute: typeof DashboardStudentsNewLazyRoute
  DashboardStaffsIndexRoute: typeof DashboardStaffsIndexRoute
  DashboardStudentsIndexRoute: typeof DashboardStudentsIndexRoute
  DashboardStaffsStaffIdEditRoute: typeof DashboardStaffsStaffIdEditRoute
}

const DashboardRouteChildren: DashboardRouteChildren = {
  DashboardCameraRoute: DashboardCameraRoute,
  DashboardProfileRoute: DashboardProfileRoute,
  DashboardStaffRecordsRoute: DashboardStaffRecordsRoute,
  DashboardStatisticsRoute: DashboardStatisticsRoute,
  DashboardStudentRecordsRoute: DashboardStudentRecordsRoute,
  DashboardTrainingImagesRoute: DashboardTrainingImagesRoute,
  DashboardIndexRoute: DashboardIndexRoute,
  DashboardStaffsStaffIdRoute: DashboardStaffsStaffIdRoute,
  DashboardStudentsStudentIdRoute: DashboardStudentsStudentIdRoute,
  DashboardStaffsNewLazyRoute: DashboardStaffsNewLazyRoute,
  DashboardStudentsNewLazyRoute: DashboardStudentsNewLazyRoute,
  DashboardStaffsIndexRoute: DashboardStaffsIndexRoute,
  DashboardStudentsIndexRoute: DashboardStudentsIndexRoute,
  DashboardStaffsStaffIdEditRoute: DashboardStaffsStaffIdEditRoute,
}

const DashboardRouteWithChildren = DashboardRoute._addFileChildren(
  DashboardRouteChildren,
)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/dashboard': typeof DashboardRouteWithChildren
  '/login': typeof LoginRoute
  '/dashboard/camera': typeof DashboardCameraRoute
  '/dashboard/profile': typeof DashboardProfileRoute
  '/dashboard/staff-records': typeof DashboardStaffRecordsRoute
  '/dashboard/statistics': typeof DashboardStatisticsRoute
  '/dashboard/student-records': typeof DashboardStudentRecordsRoute
  '/dashboard/training-images': typeof DashboardTrainingImagesRoute
  '/dashboard/': typeof DashboardIndexRoute
  '/dashboard/staffs/$staffId': typeof DashboardStaffsStaffIdRoute
  '/dashboard/students/$studentId': typeof DashboardStudentsStudentIdRoute
  '/dashboard/staffs/new': typeof DashboardStaffsNewLazyRoute
  '/dashboard/students/new': typeof DashboardStudentsNewLazyRoute
  '/dashboard/staffs': typeof DashboardStaffsIndexRoute
  '/dashboard/students': typeof DashboardStudentsIndexRoute
  '/dashboard/staffs/$staffId/edit': typeof DashboardStaffsStaffIdEditRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/login': typeof LoginRoute
  '/dashboard/camera': typeof DashboardCameraRoute
  '/dashboard/profile': typeof DashboardProfileRoute
  '/dashboard/staff-records': typeof DashboardStaffRecordsRoute
  '/dashboard/statistics': typeof DashboardStatisticsRoute
  '/dashboard/student-records': typeof DashboardStudentRecordsRoute
  '/dashboard/training-images': typeof DashboardTrainingImagesRoute
  '/dashboard': typeof DashboardIndexRoute
  '/dashboard/staffs/$staffId': typeof DashboardStaffsStaffIdRoute
  '/dashboard/students/$studentId': typeof DashboardStudentsStudentIdRoute
  '/dashboard/staffs/new': typeof DashboardStaffsNewLazyRoute
  '/dashboard/students/new': typeof DashboardStudentsNewLazyRoute
  '/dashboard/staffs': typeof DashboardStaffsIndexRoute
  '/dashboard/students': typeof DashboardStudentsIndexRoute
  '/dashboard/staffs/$staffId/edit': typeof DashboardStaffsStaffIdEditRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/dashboard': typeof DashboardRouteWithChildren
  '/login': typeof LoginRoute
  '/dashboard/camera': typeof DashboardCameraRoute
  '/dashboard/profile': typeof DashboardProfileRoute
  '/dashboard/staff-records': typeof DashboardStaffRecordsRoute
  '/dashboard/statistics': typeof DashboardStatisticsRoute
  '/dashboard/student-records': typeof DashboardStudentRecordsRoute
  '/dashboard/training-images': typeof DashboardTrainingImagesRoute
  '/dashboard/': typeof DashboardIndexRoute
  '/dashboard/staffs/$staffId': typeof DashboardStaffsStaffIdRoute
  '/dashboard/students/$studentId': typeof DashboardStudentsStudentIdRoute
  '/dashboard/staffs/new': typeof DashboardStaffsNewLazyRoute
  '/dashboard/students/new': typeof DashboardStudentsNewLazyRoute
  '/dashboard/staffs/': typeof DashboardStaffsIndexRoute
  '/dashboard/students/': typeof DashboardStudentsIndexRoute
  '/dashboard/staffs_/$staffId/edit': typeof DashboardStaffsStaffIdEditRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/dashboard'
    | '/login'
    | '/dashboard/camera'
    | '/dashboard/profile'
    | '/dashboard/staff-records'
    | '/dashboard/statistics'
    | '/dashboard/student-records'
    | '/dashboard/training-images'
    | '/dashboard/'
    | '/dashboard/staffs/$staffId'
    | '/dashboard/students/$studentId'
    | '/dashboard/staffs/new'
    | '/dashboard/students/new'
    | '/dashboard/staffs'
    | '/dashboard/students'
    | '/dashboard/staffs/$staffId/edit'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/login'
    | '/dashboard/camera'
    | '/dashboard/profile'
    | '/dashboard/staff-records'
    | '/dashboard/statistics'
    | '/dashboard/student-records'
    | '/dashboard/training-images'
    | '/dashboard'
    | '/dashboard/staffs/$staffId'
    | '/dashboard/students/$studentId'
    | '/dashboard/staffs/new'
    | '/dashboard/students/new'
    | '/dashboard/staffs'
    | '/dashboard/students'
    | '/dashboard/staffs/$staffId/edit'
  id:
    | '__root__'
    | '/'
    | '/dashboard'
    | '/login'
    | '/dashboard/camera'
    | '/dashboard/profile'
    | '/dashboard/staff-records'
    | '/dashboard/statistics'
    | '/dashboard/student-records'
    | '/dashboard/training-images'
    | '/dashboard/'
    | '/dashboard/staffs/$staffId'
    | '/dashboard/students/$studentId'
    | '/dashboard/staffs/new'
    | '/dashboard/students/new'
    | '/dashboard/staffs/'
    | '/dashboard/students/'
    | '/dashboard/staffs_/$staffId/edit'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  DashboardRoute: typeof DashboardRouteWithChildren
  LoginRoute: typeof LoginRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  DashboardRoute: DashboardRouteWithChildren,
  LoginRoute: LoginRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.jsx",
      "children": [
        "/",
        "/dashboard",
        "/login"
      ]
    },
    "/": {
      "filePath": "index.jsx"
    },
    "/dashboard": {
      "filePath": "dashboard.jsx",
      "children": [
        "/dashboard/camera",
        "/dashboard/profile",
        "/dashboard/staff-records",
        "/dashboard/statistics",
        "/dashboard/student-records",
        "/dashboard/training-images",
        "/dashboard/",
        "/dashboard/staffs/$staffId",
        "/dashboard/students/$studentId",
        "/dashboard/staffs/new",
        "/dashboard/students/new",
        "/dashboard/staffs/",
        "/dashboard/students/",
        "/dashboard/staffs_/$staffId/edit"
      ]
    },
    "/login": {
      "filePath": "login.jsx"
    },
    "/dashboard/camera": {
      "filePath": "dashboard/camera.jsx",
      "parent": "/dashboard"
    },
    "/dashboard/profile": {
      "filePath": "dashboard/profile.jsx",
      "parent": "/dashboard"
    },
    "/dashboard/staff-records": {
      "filePath": "dashboard/staff-records.jsx",
      "parent": "/dashboard"
    },
    "/dashboard/statistics": {
      "filePath": "dashboard/statistics.jsx",
      "parent": "/dashboard"
    },
    "/dashboard/student-records": {
      "filePath": "dashboard/student-records.jsx",
      "parent": "/dashboard"
    },
    "/dashboard/training-images": {
      "filePath": "dashboard/training-images.jsx",
      "parent": "/dashboard"
    },
    "/dashboard/": {
      "filePath": "dashboard/index.jsx",
      "parent": "/dashboard"
    },
    "/dashboard/staffs/$staffId": {
      "filePath": "dashboard/staffs/$staffId.jsx",
      "parent": "/dashboard"
    },
    "/dashboard/students/$studentId": {
      "filePath": "dashboard/students/$studentId.jsx",
      "parent": "/dashboard"
    },
    "/dashboard/staffs/new": {
      "filePath": "dashboard/staffs/new.lazy.jsx",
      "parent": "/dashboard"
    },
    "/dashboard/students/new": {
      "filePath": "dashboard/students/new.lazy.jsx",
      "parent": "/dashboard"
    },
    "/dashboard/staffs/": {
      "filePath": "dashboard/staffs/index.jsx",
      "parent": "/dashboard"
    },
    "/dashboard/students/": {
      "filePath": "dashboard/students/index.jsx",
      "parent": "/dashboard"
    },
    "/dashboard/staffs_/$staffId/edit": {
      "filePath": "dashboard/staffs_/$staffId/edit.jsx",
      "parent": "/dashboard"
    }
  }
}
ROUTE_MANIFEST_END */

import AppSidebar from "@/components/app-sidebar";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { createFileRoute, Outlet, redirect } from "@tanstack/react-router";
import Switcher from "@/components/switcher.jsx";
import { getToken } from "@/store/auth/auth-store";
import { NavUser } from "@/components/nav-user";

export const Route = createFileRoute("/dashboard")({
  beforeLoad: ({ location }) => {
    const token = getToken();
    if (!token) {
      throw redirect({
        to: "/login",
        search: { redirect: location.href },
      });
    }
  },
  component: RouteComponent,
});

function RouteComponent() {
  return (
    <SidebarProvider>
      <AppSidebar />
      <main className="w-full bg-[#F9F9FB]">
        <div className="h-12 w-full border-b-slate-300 border flex items-center justify-between">
          <SidebarTrigger />
          <div className="flex gap-4 pr-3">
            <Switcher />
            <NavUser />
          </div>
        </div>
        <Outlet />
      </main>
    </SidebarProvider>
  );
}

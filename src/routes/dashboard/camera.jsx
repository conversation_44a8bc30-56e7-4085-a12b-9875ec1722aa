// import { createFileRoute } from "@tanstack/react-router";
// import { useState, useEffect, useRef } from "react";
// import html2canvas from "html2canvas";
// import { Button } from "@/components/ui/button";
// import {
//   Select,
//   SelectTrigger,
//   SelectValue,
//   SelectContent,
//   SelectGroup,
//   SelectLabel,
//   SelectItem,
// } from "@/components/ui/select";
// import { Focus } from "lucide-react";
// import { useTranslation } from "react-i18next";

// import { FaceMesh } from "@mediapipe/face_mesh";
// import { Camera as MpCamera } from "@mediapipe/camera_utils";
// import { usePredictStaff, useRecordAction } from "@/api/staff/queries";

// export const Route = createFileRoute("/dashboard/camera")({
//   component: Camera,
// });

// function Camera() {
//   const [stream, setStream] = useState(null);
//   const [imageSrc, setImageSrc] = useState(null);
//   const [devices, setDevices] = useState([]);
//   const [selectedDeviceId, setSelectedDeviceId] = useState("");
//   const [identifiedPerson, setIdentifiedPerson] = useState(null);

//   const videoRef = useRef(null);
//   const canvasRef = useRef(null);
//   const hasCapturedRef = useRef(false); // guard for auto-capture
//   const { t } = useTranslation();
//   const {
//     isPending: isPredictLoading,
//     isError: isPredictError,
//     reset: resetPredictError,
//   } = usePredictStaff();
//   const { mutateAsync: recordAction, isPending: isRecordLoading } =
//     useRecordAction();

//   const resetCapture = () => {
//     setImageSrc(null);
//     setIdentifiedPerson(null);
//     hasCapturedRef.current = false;
//     resetPredictError();
//   };

//   // Get available video devices (cameras) and set default one
//   const getVideoDevices = async () => {
//     try {
//       await navigator.mediaDevices.getUserMedia({ video: true });
//       const allDevices = await navigator.mediaDevices.enumerateDevices();
//       const videoDevices = allDevices.filter(
//         (device) => device.kind === "videoinput"
//       );
//       setDevices(videoDevices);
//       if (videoDevices.length > 0) {
//         setSelectedDeviceId(videoDevices[0].deviceId);
//       }
//     } catch (error) {
//       console.error("Error fetching camera devices:", error);
//     }
//   };

//   // Start video stream with the selected device
//   const startCamera = async (deviceId) => {
//     try {
//       const mediaStream = await navigator.mediaDevices.getUserMedia({
//         video: { deviceId },
//         audio: false,
//       });
//       setStream(mediaStream);
//     } catch (error) {
//       console.error("Error accessing camera:", error);
//     }
//   };

//   // Initialize MediaPipe FaceMesh
//   const setupFaceMesh = () => {
//     if (!videoRef.current) return;

//     const faceMesh = new FaceMesh({
//       locateFile: (file) =>
//         `https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh/${file}`,
//     });

//     faceMesh.setOptions({
//       maxNumFaces: 2,
//       refineLandmarks: true,
//       minDetectionConfidence: 0.7,
//       minTrackingConfidence: 0.5,
//     });

//     faceMesh.onResults(onFaceResults);

//     // Use MediaPipe Camera Utils to send frames
//     const mpCamera = new MpCamera(videoRef.current, {
//       onFrame: async () => {
//         await faceMesh.send({ image: videoRef.current });
//       },
//       width: 640,
//       height: 480,
//     });
//     mpCamera.start();
//   };

//   // Callback for MediaPipe FaceMesh results
//   const onFaceResults = (results) => {
//     if (!canvasRef.current) return;
//     const canvas = canvasRef.current;
//     const ctx = canvas.getContext("2d");
//     if (!ctx) return;

//     // resize canvas to match video
//     if (
//       canvas.width !== videoRef.current.videoWidth ||
//       canvas.height !== videoRef.current.videoHeight
//     ) {
//       canvas.width = videoRef.current.videoWidth;
//       canvas.height = videoRef.current.videoHeight;
//     }

//     // draw video frame
//     ctx.clearRect(0, 0, canvas.width, canvas.height);
//     ctx.drawImage(results.image, 0, 0, canvas.width, canvas.height);

//     const faces = results.multiFaceLandmarks || [];

//     // if no faces detected, allow next capture
//     if (faces.length === 0) {
//       hasCapturedRef.current = false;
//       return;
//     }

//     // for each detected face
//     faces.forEach((landmarks) => {
//       // optional: draw landmarks
//       landmarks.forEach((lm) => {
//         const x = lm.x * canvas.width;
//         const y = lm.y * canvas.height;
//         ctx.beginPath();
//         ctx.arc(x, y, 1, 0, 2 * Math.PI);
//         ctx.fillStyle = "#00FF00";
//         ctx.fill();
//       });

//       // compute normalized bounding-box width
//       const xs = landmarks.map((lm) => lm.x);
//       const minX = Math.min(...xs);
//       const maxX = Math.max(...xs);
//       const boxWidthNorm = maxX - minX;

//       // trigger an auto-capture when face is big enough
//       const THRESHOLD = 0.3; // e.g. 30% of frame width
//       if (!hasCapturedRef.current && boxWidthNorm > THRESHOLD) {
//         hasCapturedRef.current = true;
//         setTimeout(() => takePicture(), 200);
//       }
//     });
//   };

//   useEffect(() => {
//     getVideoDevices();
//   }, []);

//   useEffect(() => {
//     if (selectedDeviceId) {
//       startCamera(selectedDeviceId);
//     }
//   }, [selectedDeviceId]);

//   // When stream is ready, attach it to the video element and setup face mesh
//   useEffect(() => {
//     if (stream && videoRef.current) {
//       videoRef.current.srcObject = stream;
//       videoRef.current.onloadedmetadata = () => {
//         videoRef.current.play();
//         setupFaceMesh();
//       };
//     }
//   }, [stream]);

//   const takePicture = async () => {
//     if (!videoRef.current) return;
//     try {
//       const canvasSnap = await html2canvas(videoRef.current);
//       const dataUrl = canvasSnap.toDataURL("image/jpeg");
//       setImageSrc(dataUrl);
//       setIdentifiedPerson(null);

//       const blob = await (await fetch(dataUrl)).blob();
//       const file = new File([blob], "capture.jpg", { type: blob.type });
//       const formData = new FormData();
//       formData.append("file", file);

//       predictFace(formData, {
//         onSuccess: (res) => {
//           const p = res.person;
//           setIdentified({ id: p.id, fullName: `${p.name} ${p.surname}` });
//         },
//         onError: (error) => {
//           console.error("Prediction error:", error);
//         },
//       });
//     } catch (error) {
//       console.error("Error taking picture:", error);
//     }
//   };

//   const handleEnter = () => {
//     if (!identifiedPerson) return;
//     recordAction(
//       {
//         security_person_id: 1,
//         action: "in",
//         method: "face",
//         person_id: identifiedPerson.id,
//       },
//       { onSuccess: resetCapture }
//     );
//   };

//   const handleExit = () => {
//     if (!identifiedPerson) return;
//     recordAction(
//       {
//         security_person_id: 1,
//         action: "out",
//         method: "face",
//         person_id: identifiedPerson.id,
//       },
//       { onSuccess: resetCapture }
//     );
//   };

//   return (
//     <div className="max-w-[1400px] mx-auto p-4 sm:p-6 flex flex-col">
//       <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
//         <h1 className="text-2xl text-slate-700 font-semibold">
//           {t("appSidebar.main.camera")}
//         </h1>

//         {/* Camera Selection */}
//         <div className="flex flex-col md:flex-row items-center gap-2">
//           <label className="text-gray-700 font-medium">
//             {t("chooseCamera")}
//           </label>
//           <Select
//             value={selectedDeviceId}
//             onValueChange={(value) => setSelectedDeviceId(value)}
//             disabled={!devices.length}
//           >
//             <SelectTrigger className="w-[250px]">
//               <SelectValue placeholder="Select a camera" />
//             </SelectTrigger>
//             <SelectContent>
//               <SelectGroup>
//                 <SelectLabel>{t("cameras")}</SelectLabel>
//                 {devices.map((device) => (
//                   <SelectItem key={device.deviceId} value={device.deviceId}>
//                     {device.label || `Camera (${device.deviceId})`}
//                   </SelectItem>
//                 ))}
//               </SelectGroup>
//             </SelectContent>
//           </Select>
//         </div>
//       </div>

//       {/* Video and Canvas elements */}
//       <div className="flex flex-col lg:flex-row justify-center gap-4 mt-4 relative">
//         {/* Video and Canvas elements */}
//         <div className="min-w-max mx-auto relative">
//           <video
//             ref={videoRef}
//             autoPlay
//             style={{ height: "480px", width: "640px" }}
//             className="rounded-lg shadow-md bg-black"
//           />
//           <canvas
//             ref={canvasRef}
//             style={{
//               position: "absolute",
//               top: 0,
//               left: 0,
//               height: "480px",
//               width: "640px",
//               pointerEvents: "none",
//             }}
//           />
//           {imageSrc && (
//             <img
//               src={imageSrc}
//               style={{ height: "180px", width: "240px" }}
//               alt="Captured"
//               className="absolute top-0 right-0 rounded-bl-sm border-l border-b border-l-black border-b-black"
//             />
//           )}
//         </div>

//         {/* Display recognized person or loading state */}
//         <div className="w-full flex justify-center items-center border border-dashed border-violet-700 rounded-lg p-4">
//           {!imageSrc && (
//             <span className="text-gray-500">{t("notCaptured")}</span>
//           )}
//           {imageSrc && isPredictLoading && (
//             <span className="text-gray-500">{t("identifying")}...</span>
//           )}
//           {imageSrc && isPredictError && (
//             <span className="text-red-600 font-semibold">User not found</span>
//           )}
//           {imageSrc &&
//             !isPredictLoading &&
//             !isPredictError &&
//             identifiedPerson && (
//               <div className="text-gray-700 font-semibold text-lg">
//                 {identifiedPerson.fullName || "Unknown"}
//               </div>
//             )}
//         </div>
//       </div>

//       {/* Action Buttons */}
//       <div className="flex items-center mt-6 justify-center gap-6">
//         <Button
//           size="lg"
//           className="w-max py-7 rounded-lg text-lg"
//           onClick={takePicture}
//           disabled={!stream || isPredictLoading}
//         >
//           <div>
//             <Focus size={20} className="text-yellow-500" />
//           </div>
//           {t("takePhoto")}
//         </Button>
//         <Button
//           size="lg"
//           className="w-32 py-7 rounded-lg bg-emerald-600 hover:bg-emerald-600/80 text-lg"
//           // onClick={() => {
//           //   resetCapture();
//           //   alert(t("studEntered"));
//           // }}
//           onClick={handleEnter}
//           disabled={!stream || isPredictLoading}
//         >
//           {t("enter")}
//         </Button>
//         <Button
//           size="lg"
//           className="w-32 py-7 rounded-lg bg-red-600 hover:bg-red-600/80 text-lg"
//           onClick={handleExit}
//           // onClick={() => {
//           //   resetCapture();
//           //   alert(t("studExited"));
//           // }}
//           disabled={!stream || isPredictLoading}
//         >
//           {t("exit")}
//         </Button>
//       </div>
//     </div>
//   );
// }

// src/routes/dashboard/Camera.jsx
import { createFileRoute } from "@tanstack/react-router";
import { useState, useEffect, useRef } from "react";
import html2canvas from "html2canvas";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectGroup,
  SelectLabel,
  SelectItem,
} from "@/components/ui/select";
import { Focus } from "lucide-react";
import { useTranslation } from "react-i18next";

import { FaceMesh } from "@mediapipe/face_mesh";
import { Camera as MpCamera } from "@mediapipe/camera_utils";

import { usePredictStaff, useStaffSecurity } from "@/api/staff/queries";
import { useRecordAction } from "@/api/staff/queries";
import { ManualRecordDialog } from "@/components/manual-record-dialog";
import { toast } from "sonner";
import { ComboboxSearch } from "@/components/combobox-search";

export const Route = createFileRoute("/dashboard/camera")({
  component: Camera,
});

export default function Camera() {
  const [stream, setStream] = useState(null);
  const [imageSrc, setImageSrc] = useState(null);
  const [devices, setDevices] = useState([]);
  const [selectedDeviceId, setSelectedDeviceId] = useState("");
  const [securityPersonId, setSecurityPersonId] = useState(() => {
    return localStorage.getItem("selectedSecurityPersonId") || "";
  });
  const [identifiedPerson, setIdentifiedPerson] = useState(null);

  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const capturedRef = useRef(false);

  const { t } = useTranslation();
  const { data: securityPersons = [], isLoading: isSecurityLoading } =
    useStaffSecurity();
  const {
    mutate: predictFace,
    isLoading: isPredictLoading,
    isError: isPredictError,
    reset: resetPredictError,
  } = usePredictStaff();
  const { mutate: recordAction, isPending: isRecordPeding } = useRecordAction();

  const resetAll = () => {
    setImageSrc(null);
    setIdentifiedPerson(null);
    capturedRef.current = false;
    resetPredictError();
  };

  // enumerate cameras
  useEffect(() => {
    (async () => {
      try {
        await navigator.mediaDevices.getUserMedia({ video: true });
        const all = await navigator.mediaDevices.enumerateDevices();
        const cams = all.filter((d) => d.kind === "videoinput");
        setDevices(cams);
        cams[0] && setSelectedDeviceId(cams[0].deviceId);
      } catch (e) {
        console.error("Could not load cameras", e);
      }
    })();
  }, []);

  // start camera when selectedDeviceId changes
  useEffect(() => {
    if (!selectedDeviceId) return;
    (async () => {
      try {
        const s = await navigator.mediaDevices.getUserMedia({
          video: { deviceId: selectedDeviceId },
        });
        setStream(s);
      } catch (e) {
        console.error("Error accessing camera", e);
      }
    })();
  }, [selectedDeviceId]);

  // hook up MediaPipe -> canvas
  useEffect(() => {
    const videoEl = videoRef.current;
    if (stream && videoEl) {
      videoEl.srcObject = stream;
      videoEl.onloadedmetadata = () => {
        videoEl.play();
        const faceMesh = new FaceMesh({
          locateFile: (f) =>
            `https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh/${f}`,
        });
        faceMesh.setOptions({
          maxNumFaces: 1,
          refineLandmarks: true,
          minDetectionConfidence: 0.7,
          minTrackingConfidence: 0.5,
        });
        faceMesh.onResults(onResults);
        new MpCamera(videoEl, {
          onFrame: async () => await faceMesh.send({ image: videoEl }),
          width: 640,
          height: 480,
        }).start();
      };
    }

    function onResults(results) {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const ctx = canvas.getContext("2d");

      // match canvas size to video
      if (
        canvas.width !== video.videoWidth ||
        canvas.height !== video.videoHeight
      ) {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
      }

      // clear & draw video frame
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.drawImage(results.image, 0, 0, canvas.width, canvas.height);

      // draw landmarks
      const faces = results.multiFaceLandmarks || [];
      if (!faces.length) {
        capturedRef.current = false;
        return;
      }
      faces[0].forEach((lm) => {
        const x = lm.x * canvas.width;
        const y = lm.y * canvas.height;
        ctx.beginPath();
        ctx.arc(x, y, 1.2, 0, 2 * Math.PI);
        ctx.fillStyle = "#00FF00";
        ctx.fill();
      });

      // auto-capture if face is close enough
      const xs = faces[0].map((lm) => lm.x);
      const span = Math.max(...xs) - Math.min(...xs);
      if (!capturedRef.current && span > 0.3) {
        capturedRef.current = true;
        setTimeout(capture, 200);
      }
    }
  }, [stream]);

  // snapshot → predict
  const capture = async () => {
    resetPredictError();
    const video = videoRef.current;
    try {
      const snap = await html2canvas(video);
      const dataUrl = snap.toDataURL("image/jpeg");
      setImageSrc(dataUrl);
      setIdentifiedPerson(null);

      const blob = await (await fetch(dataUrl)).blob();
      const file = new File([blob], "capture.jpg", { type: blob.type });
      const form = new FormData();
      form.append("file", file);

      predictFace(form, {
        onSuccess: (res) => {
          const p = res.person;
          setIdentifiedPerson({
            id: p.id,
            fullName: `${p.name} ${p.surname}`,
          });
        },
      });
    } catch (e) {
      console.error("Error capturing", e);
    }
  };

  const handleEnter = () => {
    if (!identifiedPerson || !securityPersonId) return;
    recordAction(
      {
        security_person_id: Number(securityPersonId),
        action: "in",
        method: "face",
        person_id: identifiedPerson.id,
      },
      { onSuccess: resetAll }
    );
  };

  const handleExit = () => {
    if (!identifiedPerson || !securityPersonId) return;
    recordAction(
      {
        security_person_id: Number(securityPersonId),
        action: "out",
        method: "face",
        person_id: identifiedPerson.id,
      },
      { onSuccess: resetAll }
    );
  };

  const handleManualRecord = ({ personId, action }) => {
    recordAction(
      {
        security_person_id: Number(securityPersonId),
        action,
        method: "manual",
        person_id: Number(personId),
      },
      {
        onSuccess: () => {
          resetAll();
          // toast.success(
          //   `Recorded ${action === "in" ? "entry" : "exit"} for ID ${personId}`
          // );
        },
        // onError: () => {
        //   toast.error("Failed to record manually");
        // },
      }
    );
  };

  return (
    <div className="max-w-[1400px] mx-auto p-4 sm:p-6 flex flex-col">
      {/* Header + Camera selector */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <h1 className="text-2xl font-semibold">
          {t("appSidebar.main.camera")}
        </h1>
        <div className="flex items-center gap-2">
          <ComboboxSearch
            options={securityPersons.map((sp) => ({
              label: `${sp?.person.name} ${sp?.person.surname}`,
              value: String(sp?.person.id),
            }))}
            placeholder="Select security person"
            onChange={(value) => {
              setSecurityPersonId(value);
              localStorage.setItem("selectedSecurityPersonId", value);
            }}
            value={securityPersonId}
            className="w-[250px]"
            loading={isSecurityLoading}
          />
          <Select
            value={selectedDeviceId}
            onValueChange={setSelectedDeviceId}
            disabled={!devices.length}
          >
            <SelectTrigger className="w-[250px]">
              <SelectValue placeholder="Select a camera" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>{t("cameras")}</SelectLabel>
                {devices.map((d) => (
                  <SelectItem key={d.deviceId} value={d.deviceId}>
                    {d.label || d.deviceId}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
          {/* <ComboboxForm /> */}

          <ManualRecordDialog
            triggerButton={
              <Button className="bg-slate-700 hover:bg-slate-700/90">
                +Manual
              </Button>
            }
            isRecordPending={isRecordPeding || false}
            onRecord={handleManualRecord}
          />
        </div>
      </div>

      {/* Video + overlay + result */}
      <div className="flex flex-col lg:flex-row justify-center gap-4 mt-4">
        <div className="relative bg-slate-50" width={640} height={480}>
          <video
            ref={videoRef}
            autoPlay
            style={{ height: "480px", width: "640px" }}
            className="rounded-lg bg-black shadow-md relative z-0"
          />
          <canvas
            ref={canvasRef}
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              height: "480px",
              width: "640px",
              pointerEvents: "none",
            }}
            className="rounded-lg"
          />
          {imageSrc && (
            <img
              src={imageSrc}
              alt="snapshot"
              className="absolute z-10 top-0 right-0 h-[180px] w-[240px] rounded-bl-sm border border-black"
            />
          )}
        </div>
        <div className="flex-1 flex items-center justify-center border-dashed border-2 rounded-lg p-4 border-violet-700">
          {!imageSrc && (
            <span className="text-gray-500">{t("notCaptured")}</span>
          )}
          {imageSrc && isPredictLoading && (
            <span className="text-gray-500">{t("identifying")}…</span>
          )}
          {imageSrc && isPredictError && (
            <span className="text-red-600 font-semibold">User not found</span>
          )}
          {imageSrc &&
            !isPredictLoading &&
            !isPredictError &&
            identifiedPerson && (
              <span className="text-lg font-semibold">
                {identifiedPerson.fullName}
              </span>
            )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-center gap-6 mt-6">
        <Button
          size="lg"
          onClick={capture}
          disabled={!stream || isPredictLoading}
          className="py-7 rounded-lg text-lg"
        >
          <Focus size={20} className="text-yellow-500" /> {t("takePhoto")}
        </Button>
        <Button
          size="lg"
          onClick={handleEnter}
          disabled={!identifiedPerson}
          className="bg-emerald-600 hover:bg-emerald-500 text-lg py-7 rounded-lg w-32"
        >
          {t("enter")}
        </Button>
        <Button
          size="lg"
          onClick={handleExit}
          disabled={!identifiedPerson}
          className="bg-red-600 hover:bg-red-500 text-lg py-7 rounded-lg w-32"
        >
          {t("exit")}
        </Button>
      </div>
    </div>
  );
}

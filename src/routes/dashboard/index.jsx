import { createFileRoute } from "@tanstack/react-router";
import Widget from "@/components/widget.jsx";
import { useTranslation } from "react-i18next";
import { useStats } from "@/api/stats/queries";

export const Route = createFileRoute("/dashboard/")({
  component: RouteComponent,
});

function RouteComponent() {
  const { t } = useTranslation();
  const { data, isLoading, error } = useStats();
  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div className="max-w-[1400px] min-h-screen mx-auto p-4 sm:p-6 ">
      <h1 className="text-xl pl-2 mb-4 font-medium text-gray-700">
        {t("studentsData")}
      </h1>
      <div className="flex flex-col items-center sm:flex-row justify-start gap-6 md:gap-10 sm:items-stretch">
        <Widget
          num={data.student_on_campus}
          all={data.total_students}
          title={t("onCampus")}
          label={"on_campus"}
          navigationLink={"/dashboard/students?on_campus=true"}
        />
        <Widget
          num={data.student_off_campus}
          all={data.total_students}
          title={t("offCampus")}
          label={"not_on_campus"}
          navigationLink={"/dashboard/students?on_campus=false"}
        />
        <Widget
          num={data.total_students}
          all={data.total_students}
          title={t("total")}
          label={"total"}
          navigationLink={"/dashboard/staffs"}
        />
      </div>
      <h1 className="text-xl pl-2 mb-4 font-medium text-gray-700 mt-16">
        {t("stuffData")}
      </h1>
      <div className="flex flex-col items-center sm:flex-row justify-start gap-6 md:gap-10 sm:items-stretch">
        <Widget
          num={data.staff_on_campus}
          all={data.total_staff}
          title={t("onCampus")}
          label={"on_campus"}
          navigationLink={"/dashboard/staffs?on_campus=true"}
        />
        <Widget
          num={data.staff_off_campus}
          all={data.total_staff}
          title={t("offCampus")}
          label={"not_on_campus"}
          navigationLink={"/dashboard/staffs?on_campus=false"}
        />
        <Widget
          num={data.total_staff}
          all={data.total_staff}
          title={t("total")}
          label={"total"}
          navigationLink={"/dashboard/staffs"}
        />
      </div>
    </div>
  );
}

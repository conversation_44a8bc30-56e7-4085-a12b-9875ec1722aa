import { createFileRoute } from '@tanstack/react-router'
import UserProfile from "@/components/user-profile/user-profile.jsx";
import UsernameUpdateForm from "@/components/user-profile/username-update-form.jsx";
import PasswordUpdateForm from "@/components/user-profile/password-update-form.jsx";
import {getUser} from "@/store/auth/auth-store.js";
import {useChangePassword, useChangeUsername} from "@/api/person/queries.js";

export const Route = createFileRoute('/dashboard/profile')({
  component: RouteComponent,
})

function RouteComponent() {
  // const { userId } = Route.useParams()
  const { person: user } = getUser();
  const userData = {
    name: user?.name + " " + user?.surname,
    role: user?.person?.role || "superuser",
    username: user?.username || user?.email,
    avatar:
        user?.profile_picture ||
        "https://i.pinimg.com/736x/40/98/2a/40982a8167f0a53dedce3731178f2ef5.jpg",
  };
  const { mutate: mutateUsername, isPending: isUsernamePending } = useChangeUsername();
  const handleUsernameSubmit = async (data) => {
    console.log(data)
    mutateUsername({ new_username: data.new_username });
  };

  const { mutate: mutatePassword, isPending: isPasswordPending } = useChangePassword();
  const handlePasswordSubmit = async (data) => {
    console.log(data)
    mutatePassword({ old_password: data.old_password,
    new_password: data.new_password});
  };

  return <div className="max-w-[1400px] min-h-screen mx-auto p-4 sm:p-6 flex flex-col gap-6">
    <div className="flex flex-col lg:flex-row justify-between gap-6">
      <UserProfile userData={userData} />
      <div className="flex-1 rounded-md border bg-white p-4 sm:p-6">
        <UsernameUpdateForm onSubmit={handleUsernameSubmit} isLoading={isUsernamePending}/>
      </div>
    </div>
    <div className="rounded-md border bg-white p-4 sm:p-6">
      <PasswordUpdateForm onSubmit={handlePasswordSubmit} isLoading={isPasswordPending}/>
    </div>
  </div>
}

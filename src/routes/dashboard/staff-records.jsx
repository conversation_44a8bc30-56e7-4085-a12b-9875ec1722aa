import StaffRecordsTable from "@/components/staff/records/staff-records-table";
import { createFileRoute } from "@tanstack/react-router";
import { z } from "zod";
// import { useTranslation } from "react-i18next";

export const Route = createFileRoute("/dashboard/staff-records")({
  validateSearch: z.object({
    page: z.coerce.number().min(1).default(1),
    pageSize: z.coerce.number().min(1).default(10),
  }),
  component: RouteComponent,
});

function RouteComponent() {
  //   const { t } = useTranslation();

  return (
    <div className="max-w-[1400px] mx-auto p-4 sm:p-6">
      <h1 className="text-xl mb-4 font-medium text-gray-700">Staff Records</h1>
      <StaffRecordsTable routeId={Route.id} />
    </div>
  );
}

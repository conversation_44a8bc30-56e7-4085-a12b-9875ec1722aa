import StaffProfile from "@/components/staff-profile";
import StaffProfileImages from "@/components/staff-profile-images";
import { createFileRoute } from "@tanstack/react-router";
import { useStaffById } from "@/api/staff/queries";
import StaffSelfRecordsTable from "@/components/staff/self-records/staff-self-records-table";

export const Route = createFileRoute("/dashboard/staffs/$staffId")({
  component: RouteComponent,
});

function RouteComponent() {
  const { staffId } = Route.useParams();

  const { data, isLoading, error } = useStaffById(staffId);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (error || !data) {
    return <div>{error.response.data.detail}</div>;
  }

  return (
    <div className="max-w-[1400px] min-h-screen mx-auto p-4 pb-10 sm:p-6 flex flex-col gap-6">
      <div className="flex flex-col lg:flex-row justify-between gap-6">
        <div className="flex-1 rounded-md border bg-white p-4 sm:p-6">
          <StaffProfile data={data} />
        </div>

        <div className="flex-1 rounded-md border bg-white p-4 sm:p-6">
          <StaffProfileImages id={data?.person_id} />
        </div>
      </div>

      {/* Staff's Latest Records */}
      <div>
        <h2 className="mb-2 text-xl font-semibold text-slate-700">
          Latest records
        </h2>
        <StaffSelfRecordsTable personId={data?.person.id} />
      </div>
    </div>
  );
}

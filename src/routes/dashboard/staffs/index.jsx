import { createFileRoute } from "@tanstack/react-router";
import { z } from "zod";
import StaffsTable from "@/components/user-tables/staffs-table/staffs-table";
import { useTranslation } from "react-i18next";

export const Route = createFileRoute("/dashboard/staffs/")({
  validateSearch: z.object({
    page: z.coerce.number().min(1).default(1),
    pageSize: z.coerce.number().min(1).default(10),
    search: z.coerce.string().default(""),
    on_campus: z.coerce.string().default(""),
    department: z.coerce.string().default(""),
  }),
  component: StaffsList,
});

function StaffsList() {
  const { t } = useTranslation();

  return (
    <div className="max-w-[1400px] mx-auto p-4 sm:p-6">
      <h1 className="text-xl mb-4 font-medium text-gray-700">
        {t("stuffsList")}
      </h1>
      <StaffsTable routeId={Route.id} />
    </div>
  );
}

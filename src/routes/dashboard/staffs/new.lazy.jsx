import { useCreateStaff } from "@/api/staff/queries";
import EmployeeForm from "@/components/forms/staff-form/staff-form";
import { createLazyFileRoute } from "@tanstack/react-router";
import { useTranslation } from "react-i18next";

export const Route = createLazyFileRoute("/dashboard/staffs/new")({
  component: RouteComponent,
});

function RouteComponent() {
  const { t } = useTranslation();
  const { mutateAsync, isPending } = useCreateStaff();
  const handleSubmit = async (data) => {
    await mutateAsync(data);
  };

  return (
    <div className="w-full pb-6 px-4">
      <h1 className="text-center text-2xl pt-6 pb-4">
        {t("staff.createFormTitle")}
      </h1>
      <EmployeeForm onSubmit={handleSubmit} isLoading={isPending} />
    </div>
  );
}

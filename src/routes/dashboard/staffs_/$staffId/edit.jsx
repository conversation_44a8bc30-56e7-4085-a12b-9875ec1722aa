import { useStaffById, useUpdateStaff } from "@/api/staff/queries";
import EmployeeForm from "@/components/forms/staff-form/staff-form";
import { createFileRoute } from "@tanstack/react-router";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

export const Route = createFileRoute("/dashboard/staffs_/$staffId/edit")({
  component: RouteComponent,
});

function RouteComponent() {
  const { staffId } = Route.useParams();
  const { data, isLoading, error } = useStaffById(staffId);
  const updateStaffMutation = useUpdateStaff();
  const { t } = useTranslation();

  if (isLoading) {
    return <div>Загрузка данных..</div>;
  }
  if (error || !data) {
    return <div>Ошибка загрузки данных.</div>;
  }

  const handleUpdateStaff = async (formData) => {
    console.log("formData", formData);
    try {
      await updateStaffMutation.mutateAsync({
        id: staffId,
        updatedData: formData,
      });
      toast.success("Успешно обновлен");
    } catch (error) {
      toast.error("Ошибка при обновлении фестиваля");
    }
  };

  return (
    <div className="w-full pb-6 px-4">
      <h1 className="text-center text-2xl pt-6 pb-4">
        {t("staff.updateFormTitle")}
      </h1>
      <EmployeeForm
        isCreateForm={false}
        initialData={data}
        onSubmit={handleUpdateStaff}
      />
    </div>
  );
}

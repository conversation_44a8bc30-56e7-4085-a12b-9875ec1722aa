import { StudentsRecordsTable } from "@/components/student/records/students-records-table";
import { createFileRoute } from "@tanstack/react-router";
import { useTranslation } from "react-i18next";
import { z } from "zod";

export const Route = createFileRoute("/dashboard/student-records")({
  validateSearch: z.object({
    page: z.coerce.number().min(1).default(1),
    pageSize: z.coerce.number().min(1).default(10),
  }),
  component: RouteComponent,
});

function RouteComponent() {
  const { t } = useTranslation();
  return (
    <div className="max-w-[1400px] mx-auto p-4 sm:p-6">
      <h1 className="text-xl mb-4 font-medium text-gray-700">
        {t("recordsStudents")}
      </h1>
      <StudentsRecordsTable routeId={Route.id} />
    </div>
  );
}

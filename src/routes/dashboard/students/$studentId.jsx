import { createFileRoute } from "@tanstack/react-router";
import StudentProfile from "../../../components/student-profile.jsx";
import { useTranslation } from "react-i18next";
import StudentProfileImages from "@/components/student-profile-images.jsx";
import { useStudentById } from "@/api/students/queries.js";
import StudentSelfRecordsTable from "@/components/student/self-records/staff-self-records-table.jsx";

export const Route = createFileRoute("/dashboard/students/$studentId")({
  component: RouteComponent,
});

function RouteComponent() {
  const { t } = useTranslation();
  const { studentId } = Route.useParams();

  const { data, isLoading, error } = useStudentById(studentId);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (error || !data) {
    return <div>{error.response.data.detail}</div>;
  }

  return (
    <div className="max-w-[1400px] min-h-screen mx-auto p-4 sm:p-6 flex flex-col gap-6">
      <div className="flex flex-col lg:flex-row justify-between gap-6">
        <StudentProfile data={data} />
        {/* Student's images for training face recognition model */}
        <div className="flex-1 rounded-md border bg-white p-4 sm:p-6">
          <StudentProfileImages id={data?.person.id} />
        </div>
      </div>

      {/* Latest records of the student with studentID */}
      <div>
        <h2 className="mb-2 text-xl font-semibold text-slate-700">
          {t("latestRecords")}
        </h2>
        <StudentSelfRecordsTable personId={data?.person.id} />
      </div>
    </div>
  );
}

import StudentsTable from "@/components/user-tables/students-table/stutents-table";
import { createFileRoute } from "@tanstack/react-router";
import { useTranslation } from "react-i18next";
import { z } from "zod";

export const Route = createFileRoute("/dashboard/students/")({
  validateSearch: z.object({
    page: z.coerce.number().min(1).default(1),
    pageSize: z.coerce.number().min(1).default(10),
    search: z.coerce.string().default(""),
    on_campus: z.coerce.string().default(""),
    department: z.coerce.string().default(""),
  }),
  component: StudentsList,
});

function StudentsList() {
  const { t } = useTranslation();
  return (
    <div className="max-w-[1400px] mx-auto p-4 sm:p-6">
      <h1 className="text-xl mb-4 font-medium text-gray-700">
        {t("studList")}
      </h1>
      <StudentsTable routeId={Route.id} />
    </div>
  );
}

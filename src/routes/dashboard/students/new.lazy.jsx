import { useCreateStudent } from "@/api/students/queries";
import UserForm from "@/components/forms/user-form/user-form";
import { createLazyFileRoute } from "@tanstack/react-router";
import { useTranslation } from "react-i18next";

export const Route = createLazyFileRoute("/dashboard/students/new")({
  component: RouteComponent,
});

function RouteComponent() {
  const { t } = useTranslation();
  const { mutateAsync, isPending } = useCreateStudent();
  const handleSubmit = async (data) => {
    await mutateAsync(data);
  };
  return (
    <div className="w-full">
      <h1 className="text-center text-2xl pt-6 pb-4">
        {t("studRegistration")}
      </h1>
      <UserForm onSubmit={handleSubmit} isLoading={false} />
    </div>
  );
}

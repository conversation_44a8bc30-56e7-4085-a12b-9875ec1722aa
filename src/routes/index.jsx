import { Button } from "@/components/ui/button";
import { createFileRoute, <PERSON> } from "@tanstack/react-router";
import { LayoutDashboard } from "lucide-react";

export const Route = createFileRoute("/")({
  component: Index,
});

function Index() {
  return (
    <div className="h-screen w-full flex justify-center items-center">
      <div className="flex flex-col justify-center items-center gap-2">
        <img
          src="/logo1.png"
          alt="Face App Logo"
          width={150}
          height={150}
          className="mx-auto"
        ></img>
        <h1 className="text-xl font-medium text-slate-500">
          Face Recognition System
        </h1>
        <Link to={"/dashboard"}>
          <Button size="lg">
            <LayoutDashboard />
            <span>Go to Dashboard</span>
          </Button>
        </Link>
      </div>
    </div>
  );
}

import { create } from "zustand";
import { persist } from "zustand/middleware";

export const authStore = create(
  persist(
    (set) => ({
      token: null,
      user: null,
      signIn: (data) =>
        set({
          user: data.user,
          token: data.access_token,
        }),
      logout: () => set({ user: null, token: null }),
    }),
    {
      name: "auth-storage",
    }
  )
);

// Selectors
const tokenSelector = (state) => state.token;
const userSelector = (state) => state.user;

// Getters
export const getToken = () => tokenSelector(authStore.getState());
export const getUser = () => userSelector(authStore.getState());
